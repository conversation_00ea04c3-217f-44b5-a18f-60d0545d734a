# API接口规范文档 - 视觉模块

## 1. API概述

### 1.1 设计原则
- **简洁性**: 接口设计简单易用
- **一致性**: 命名和参数风格统一
- **可扩展性**: 支持未来功能扩展
- **线程安全**: 支持多线程调用
- **错误处理**: 完善的错误处理机制

### 1.2 命名规范
- **类名**: 使用PascalCase，如`VisionProcessor`
- **方法名**: 使用PascalCase，如`ConnectCamera`
- **参数名**: 使用camelCase，如`deviceId`
- **常量**: 使用UPPER_CASE，如`MAX_CAMERAS`

### 1.3 错误处理
所有API方法返回`ErrorCode`枚举值，成功返回`SUCCESS`，失败返回具体错误码。

## 2. 核心数据结构

### 2.1 基础数据类型

```cpp
namespace VisionModule {

// 错误码定义
enum class ErrorCode : int {
    SUCCESS = 0,
    
    // 系统错误 (1000-1099)
    SYSTEM_NOT_INITIALIZED = 1000,
    INVALID_PARAMETERS = 1001,
    MEMORY_ALLOCATION_FAILED = 1002,
    FILE_NOT_FOUND = 1003,
    FILE_ACCESS_DENIED = 1004,
    
    // 相机错误 (1100-1199)
    CAMERA_NOT_FOUND = 1100,
    CAMERA_CONNECTION_FAILED = 1101,
    CAMERA_DISCONNECTED = 1102,
    CAMERA_BUSY = 1103,
    CAMERA_PARAMETER_INVALID = 1104,
    
    // 标定错误 (1200-1299)
    CALIBRATION_NOT_READY = 1200,
    CALIBRATION_POINTS_INSUFFICIENT = 1201,
    CALIBRATION_CALCULATION_FAILED = 1202,
    CALIBRATION_DATA_INVALID = 1203,
    
    // 模板匹配错误 (1300-1399)
    TEMPLATE_NOT_FOUND = 1300,
    TEMPLATE_CREATION_FAILED = 1301,
    TEMPLATE_MATCHING_FAILED = 1302,
    TEMPLATE_DATA_INVALID = 1303,
    
    // 坐标转换错误 (1400-1499)
    COORDINATE_TRANSFORM_FAILED = 1400,
    CALIBRATION_MATRIX_INVALID = 1401
};

// 2D坐标结构
struct Point2D {
    double x = 0.0;
    double y = 0.0;
    
    Point2D() = default;
    Point2D(double x, double y) : x(x), y(y) {}
};

// 物理坐标结构
struct Coordinate2D {
    double x = 0.0;        // X坐标 (mm)
    double y = 0.0;        // Y坐标 (mm)
    double angle = 0.0;    // 角度 (度)
    double confidence = 0.0; // 置信度 (0.0-1.0)
    
    Coordinate2D() = default;
    Coordinate2D(double x, double y, double angle = 0.0, double confidence = 1.0)
        : x(x), y(y), angle(angle), confidence(confidence) {}
};

// 矩形区域
struct Rectangle {
    int x = 0;      // 左上角X坐标
    int y = 0;      // 左上角Y坐标
    int width = 0;  // 宽度
    int height = 0; // 高度
    
    Rectangle() = default;
    Rectangle(int x, int y, int w, int h) : x(x), y(y), width(w), height(h) {}
};

// 相机参数结构
struct CameraParameters {
    double exposure = 10.0;     // 曝光时间 (ms)
    double gain = 1.0;          // 增益
    int width = 1920;           // 图像宽度
    int height = 1080;          // 图像高度
    std::string format = "RGB8"; // 图像格式
};

// 匹配参数结构
struct MatchingParameters {
    double threshold = 0.8;     // 匹配阈值 (0.0-1.0)
    int maxMatches = 5;         // 最大匹配数量
    double angleRange = 360.0;  // 角度搜索范围 (度)
    double scaleMin = 0.8;      // 最小缩放比例
    double scaleMax = 1.2;      // 最大缩放比例
};

}
```

### 2.2 回调接口

```cpp
// 事件回调接口
class IVisionCallback {
public:
    virtual ~IVisionCallback() = default;
    
    // 图像采集回调
    virtual void OnImageCaptured(const cv::Mat& image) {}
    
    // 匹配结果回调
    virtual void OnMatchFound(const std::vector<Coordinate2D>& matches) {}
    
    // 错误回调
    virtual void OnError(ErrorCode error, const std::string& message) {}
    
    // 状态变化回调
    virtual void OnStatusChanged(const std::string& status) {}
};
```

## 3. 主要API接口

### 3.1 VisionProcessor - 主处理类

```cpp
class VisionProcessor {
public:
    // 构造和析构
    VisionProcessor();
    ~VisionProcessor();
    
    // 禁用拷贝构造和赋值
    VisionProcessor(const VisionProcessor&) = delete;
    VisionProcessor& operator=(const VisionProcessor&) = delete;
    
    // 初始化和配置
    struct InitConfig {
        std::string halconLicensePath;  // Halcon许可证路径
        std::string workingDirectory;   // 工作目录
        bool enableLogging = true;      // 是否启用日志
        std::string logLevel = "INFO";  // 日志级别
    };
    
    /**
     * @brief 初始化视觉处理器
     * @param config 初始化配置
     * @return 错误码
     */
    ErrorCode Initialize(const InitConfig& config);
    
    /**
     * @brief 设置回调接口
     * @param callback 回调接口指针
     */
    void SetCallback(IVisionCallback* callback);
    
    /**
     * @brief 获取最后一次错误信息
     * @return 错误描述字符串
     */
    std::string GetLastError() const;
    
    // 相机控制接口
    /**
     * @brief 连接相机
     * @param deviceId 相机设备ID
     * @return 错误码
     */
    ErrorCode ConnectCamera(const std::string& deviceId);
    
    /**
     * @brief 断开相机连接
     * @return 错误码
     */
    ErrorCode DisconnectCamera();
    
    /**
     * @brief 检查相机连接状态
     * @return true-已连接，false-未连接
     */
    bool IsCameraConnected() const;
    
    /**
     * @brief 获取可用相机列表
     * @param devices 输出设备列表
     * @return 错误码
     */
    ErrorCode GetAvailableCameras(std::vector<std::string>& devices);
    
    /**
     * @brief 设置相机参数
     * @param params 相机参数
     * @return 错误码
     */
    ErrorCode SetCameraParameters(const CameraParameters& params);
    
    /**
     * @brief 获取相机参数
     * @param params 输出相机参数
     * @return 错误码
     */
    ErrorCode GetCameraParameters(CameraParameters& params);
    
    /**
     * @brief 单次图像采集
     * @param image 输出图像
     * @return 错误码
     */
    ErrorCode CaptureImage(cv::Mat& image);
    
    /**
     * @brief 开始连续采集
     * @return 错误码
     */
    ErrorCode StartContinuousCapture();
    
    /**
     * @brief 停止连续采集
     * @return 错误码
     */
    ErrorCode StopContinuousCapture();
    
    // 标定接口
    /**
     * @brief 开始标定流程
     * @return 错误码
     */
    ErrorCode StartCalibration();
    
    /**
     * @brief 添加标定点
     * @param pixelCoord 像素坐标
     * @param physicalCoord 物理坐标
     * @return 错误码
     */
    ErrorCode AddCalibrationPoint(const Point2D& pixelCoord, 
                                  const Coordinate2D& physicalCoord);
    
    /**
     * @brief 计算标定矩阵
     * @return 错误码
     */
    ErrorCode CalculateCalibration();
    
    /**
     * @brief 保存标定数据
     * @param filePath 文件路径
     * @return 错误码
     */
    ErrorCode SaveCalibration(const std::string& filePath);
    
    /**
     * @brief 加载标定数据
     * @param filePath 文件路径
     * @return 错误码
     */
    ErrorCode LoadCalibration(const std::string& filePath);
    
    /**
     * @brief 检查是否已标定
     * @return true-已标定，false-未标定
     */
    bool IsCalibrated() const;
    
    /**
     * @brief 获取标定精度
     * @return 标定精度 (mm)
     */
    double GetCalibrationAccuracy() const;
    
    // 模板匹配接口
    /**
     * @brief 创建模板
     * @param image 源图像
     * @param roi 感兴趣区域
     * @param templateName 模板名称
     * @return 错误码
     */
    ErrorCode CreateTemplate(const cv::Mat& image, 
                           const Rectangle& roi,
                           const std::string& templateName);
    
    /**
     * @brief 保存模板
     * @param templateName 模板名称
     * @param filePath 文件路径
     * @return 错误码
     */
    ErrorCode SaveTemplate(const std::string& templateName,
                          const std::string& filePath);
    
    /**
     * @brief 加载模板
     * @param filePath 文件路径
     * @return 错误码
     */
    ErrorCode LoadTemplate(const std::string& filePath);
    
    /**
     * @brief 执行模板匹配
     * @param image 待匹配图像
     * @param params 匹配参数
     * @param results 输出匹配结果
     * @return 错误码
     */
    ErrorCode FindMatches(const cv::Mat& image,
                         const MatchingParameters& params,
                         std::vector<Coordinate2D>& results);
    
    // 坐标转换接口
    /**
     * @brief 像素坐标转物理坐标
     * @param pixelCoord 像素坐标
     * @param physicalCoord 输出物理坐标
     * @return 错误码
     */
    ErrorCode PixelToPhysical(const Point2D& pixelCoord,
                             Coordinate2D& physicalCoord);
    
    /**
     * @brief 物理坐标转像素坐标
     * @param physicalCoord 物理坐标
     * @param pixelCoord 输出像素坐标
     * @return 错误码
     */
    ErrorCode PhysicalToPixel(const Coordinate2D& physicalCoord,
                             Point2D& pixelCoord);
    
    // 状态查询接口
    /**
     * @brief 检查是否已初始化
     * @return true-已初始化，false-未初始化
     */
    bool IsInitialized() const;
    
    /**
     * @brief 获取版本信息
     * @return 版本字符串
     */
    static std::string GetVersion();
    
private:
    class Impl;
    std::unique_ptr<Impl> pImpl;
};
```

## 4. 使用示例

### 4.1 基本使用流程

```cpp
#include "VisionProcessor.h"
#include <iostream>

int main() {
    using namespace VisionModule;
    
    // 1. 创建视觉处理器
    VisionProcessor processor;
    
    // 2. 初始化配置
    VisionProcessor::InitConfig config;
    config.halconLicensePath = "G:/MVTec/HALCON-23.11-Progress";
    config.workingDirectory = "./vision_data";
    config.enableLogging = true;
    
    ErrorCode result = processor.Initialize(config);
    if (result != ErrorCode::SUCCESS) {
        std::cerr << "初始化失败: " << processor.GetLastError() << std::endl;
        return -1;
    }
    
    // 3. 连接相机
    result = processor.ConnectCamera("camera_0");
    if (result != ErrorCode::SUCCESS) {
        std::cerr << "相机连接失败: " << processor.GetLastError() << std::endl;
        return -1;
    }
    
    // 4. 设置相机参数
    CameraParameters params;
    params.exposure = 20.0;
    params.gain = 2.0;
    processor.SetCameraParameters(params);
    
    // 5. 加载标定数据
    result = processor.LoadCalibration("./calibration.dat");
    if (result != ErrorCode::SUCCESS) {
        std::cout << "标定数据加载失败，需要重新标定" << std::endl;
        // 执行标定流程...
    }
    
    // 6. 加载模板
    result = processor.LoadTemplate("./template.hmt");
    if (result != ErrorCode::SUCCESS) {
        std::cout << "模板加载失败，需要创建模板" << std::endl;
        // 执行模板创建流程...
    }
    
    // 7. 执行匹配
    cv::Mat image;
    result = processor.CaptureImage(image);
    if (result == ErrorCode::SUCCESS) {
        MatchingParameters matchParams;
        matchParams.threshold = 0.8;
        matchParams.maxMatches = 5;
        
        std::vector<Coordinate2D> matches;
        result = processor.FindMatches(image, matchParams, matches);
        
        if (result == ErrorCode::SUCCESS) {
            std::cout << "找到 " << matches.size() << " 个匹配目标:" << std::endl;
            for (const auto& match : matches) {
                std::cout << "位置: (" << match.x << ", " << match.y 
                         << "), 角度: " << match.angle 
                         << ", 置信度: " << match.confidence << std::endl;
            }
        }
    }
    
    // 8. 清理资源
    processor.DisconnectCamera();
    
    return 0;
}
```

### 4.2 回调使用示例

```cpp
class MyVisionCallback : public IVisionCallback {
public:
    void OnImageCaptured(const cv::Mat& image) override {
        std::cout << "图像采集完成，尺寸: " << image.cols << "x" << image.rows << std::endl;
    }
    
    void OnMatchFound(const std::vector<Coordinate2D>& matches) override {
        std::cout << "匹配完成，找到 " << matches.size() << " 个目标" << std::endl;
    }
    
    void OnError(ErrorCode error, const std::string& message) override {
        std::cerr << "错误 " << static_cast<int>(error) << ": " << message << std::endl;
    }
};

// 使用回调
MyVisionCallback callback;
processor.SetCallback(&callback);
processor.StartContinuousCapture(); // 开始连续采集，会触发回调
```

## 5. 错误处理指南

### 5.1 错误码说明

| 错误码 | 含义 | 可能原因 | 解决方案 |
|-------|------|----------|----------|
| 1000 | 系统未初始化 | 未调用Initialize | 先调用Initialize方法 |
| 1001 | 参数无效 | 传入参数错误 | 检查参数有效性 |
| 1100 | 相机未找到 | 设备ID错误 | 检查设备ID或重新扫描 |
| 1101 | 相机连接失败 | 设备被占用或故障 | 检查设备状态 |
| 1200 | 标定未就绪 | 标定点不足 | 添加足够的标定点 |
| 1300 | 模板未找到 | 模板文件不存在 | 检查文件路径 |

### 5.2 最佳实践

1. **总是检查返回值**: 每个API调用都应检查ErrorCode
2. **使用GetLastError()**: 获取详细错误信息
3. **资源管理**: 确保正确释放资源
4. **异常安全**: 使用RAII和智能指针
5. **线程安全**: 多线程环境下注意同步

## 6. 性能优化建议

### 6.1 内存管理
- 重用图像缓冲区，避免频繁分配
- 使用内存池管理大块内存
- 及时释放不需要的资源

### 6.2 并发处理
- 图像采集和处理分离到不同线程
- 使用异步回调避免阻塞
- 合理设置线程池大小

### 6.3 算法优化
- 合理设置ROI减少计算量
- 使用GPU加速（如果可用）
- 缓存模板数据避免重复加载
