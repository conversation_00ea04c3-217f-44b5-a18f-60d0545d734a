#ifndef ICAMERAINTERFACE_H
#define ICAMERAINTERFACE_H

/**
 * @file ICameraInterface.h
 * @brief 相机接口抽象类定义
 * <AUTHOR> Module Team
 * @version 1.0.0
 * @date 2025-07-04
 */

#include <string>
#include <vector>
#include <memory>
#include <functional>
#include "../core/VisionTypes.h"
#include "../core/ErrorCodes.h"

// 前向声明
namespace cv {
    class Mat;
}

namespace VisionModule {

/**
 * @brief 相机接口抽象类
 * 
 * 定义了相机操作的统一接口，支持多厂商相机SDK的适配。
 * 使用适配器模式，为不同厂商的相机提供统一的操作接口。
 */
class ICameraInterface {
public:
    virtual ~ICameraInterface() = default;
    
    /**
     * @brief 连接相机
     * @param deviceId 设备ID
     * @return 错误码
     */
    virtual ErrorCode Connect(const std::string& deviceId) = 0;
    
    /**
     * @brief 断开相机连接
     * @return 错误码
     */
    virtual ErrorCode Disconnect() = 0;
    
    /**
     * @brief 检查相机是否已连接
     * @return true-已连接，false-未连接
     */
    virtual bool IsConnected() const = 0;
    
    /**
     * @brief 获取设备信息
     * @param info 输出设备信息
     * @return 错误码
     */
    virtual ErrorCode GetDeviceInfo(DeviceInfo& info) const = 0;
    
    /**
     * @brief 单次图像采集
     * @param image 输出图像
     * @return 错误码
     */
    virtual ErrorCode CaptureImage(cv::Mat& image) = 0;
    
    /**
     * @brief 开始连续采集
     * @param callback 图像回调函数
     * @return 错误码
     */
    virtual ErrorCode StartContinuousCapture(std::function<void(const cv::Mat&, uint64_t)> callback) = 0;
    
    /**
     * @brief 停止连续采集
     * @return 错误码
     */
    virtual ErrorCode StopContinuousCapture() = 0;
    
    /**
     * @brief 检查是否正在连续采集
     * @return true-正在采集，false-未采集
     */
    virtual bool IsCapturing() const = 0;
    
    /**
     * @brief 设置曝光时间
     * @param exposure 曝光时间（毫秒）
     * @return 错误码
     */
    virtual ErrorCode SetExposure(double exposure) = 0;
    
    /**
     * @brief 获取曝光时间
     * @param exposure 输出曝光时间（毫秒）
     * @return 错误码
     */
    virtual ErrorCode GetExposure(double& exposure) const = 0;
    
    /**
     * @brief 设置增益
     * @param gain 增益值
     * @return 错误码
     */
    virtual ErrorCode SetGain(double gain) = 0;
    
    /**
     * @brief 获取增益
     * @param gain 输出增益值
     * @return 错误码
     */
    virtual ErrorCode GetGain(double& gain) const = 0;
    
    /**
     * @brief 设置图像尺寸
     * @param width 图像宽度
     * @param height 图像高度
     * @return 错误码
     */
    virtual ErrorCode SetImageSize(int width, int height) = 0;
    
    /**
     * @brief 获取图像尺寸
     * @param width 输出图像宽度
     * @param height 输出图像高度
     * @return 错误码
     */
    virtual ErrorCode GetImageSize(int& width, int& height) const = 0;
    
    /**
     * @brief 设置帧率
     * @param frameRate 帧率
     * @return 错误码
     */
    virtual ErrorCode SetFrameRate(double frameRate) = 0;
    
    /**
     * @brief 获取帧率
     * @param frameRate 输出帧率
     * @return 错误码
     */
    virtual ErrorCode GetFrameRate(double& frameRate) const = 0;
    
    /**
     * @brief 设置触发模式
     * @param triggerMode 触发模式（"Software", "Hardware", "Continuous"）
     * @return 错误码
     */
    virtual ErrorCode SetTriggerMode(const std::string& triggerMode) = 0;
    
    /**
     * @brief 获取触发模式
     * @param triggerMode 输出触发模式
     * @return 错误码
     */
    virtual ErrorCode GetTriggerMode(std::string& triggerMode) const = 0;
    
    /**
     * @brief 软件触发
     * @return 错误码
     */
    virtual ErrorCode SoftwareTrigger() = 0;
    
    /**
     * @brief 设置相机参数
     * @param parameters 相机参数
     * @return 错误码
     */
    virtual ErrorCode SetParameters(const CameraParameters& parameters) = 0;
    
    /**
     * @brief 获取相机参数
     * @param parameters 输出相机参数
     * @return 错误码
     */
    virtual ErrorCode GetParameters(CameraParameters& parameters) const = 0;
    
    /**
     * @brief 获取参数范围
     * @param paramName 参数名称
     * @param minValue 输出最小值
     * @param maxValue 输出最大值
     * @return 错误码
     */
    virtual ErrorCode GetParameterRange(const std::string& paramName, double& minValue, double& maxValue) const = 0;
    
    /**
     * @brief 重置相机参数为默认值
     * @return 错误码
     */
    virtual ErrorCode ResetToDefault() = 0;
    
    /**
     * @brief 保存相机配置
     * @param filePath 配置文件路径
     * @return 错误码
     */
    virtual ErrorCode SaveConfiguration(const std::string& filePath) = 0;
    
    /**
     * @brief 加载相机配置
     * @param filePath 配置文件路径
     * @return 错误码
     */
    virtual ErrorCode LoadConfiguration(const std::string& filePath) = 0;
    
    /**
     * @brief 获取相机状态
     * @return 相机状态
     */
    virtual CameraStatus GetStatus() const = 0;
    
    /**
     * @brief 获取最后一次错误信息
     * @return 错误描述字符串
     */
    virtual std::string GetLastError() const = 0;
    
    /**
     * @brief 获取相机厂商名称
     * @return 厂商名称
     */
    virtual std::string GetManufacturer() const = 0;
    
    /**
     * @brief 获取相机型号
     * @return 相机型号
     */
    virtual std::string GetModel() const = 0;
    
    /**
     * @brief 获取SDK版本
     * @return SDK版本字符串
     */
    virtual std::string GetSDKVersion() const = 0;
    
    /**
     * @brief 检查相机是否支持某个功能
     * @param feature 功能名称
     * @return true-支持，false-不支持
     */
    virtual bool IsFeatureSupported(const std::string& feature) const = 0;
    
    /**
     * @brief 设置错误回调函数
     * @param callback 错误回调函数
     */
    virtual void SetErrorCallback(std::function<void(ErrorCode, const std::string&)> callback) = 0;
    
    /**
     * @brief 设置状态变化回调函数
     * @param callback 状态变化回调函数
     */
    virtual void SetStatusCallback(std::function<void(CameraStatus)> callback) = 0;
};

/**
 * @brief 相机工厂接口
 * 
 * 用于创建不同厂商的相机实例
 */
class ICameraFactory {
public:
    virtual ~ICameraFactory() = default;
    
    /**
     * @brief 创建相机实例
     * @param manufacturer 厂商名称
     * @return 相机接口指针
     */
    virtual std::unique_ptr<ICameraInterface> CreateCamera(const std::string& manufacturer) = 0;
    
    /**
     * @brief 获取支持的厂商列表
     * @return 厂商名称列表
     */
    virtual std::vector<std::string> GetSupportedManufacturers() const = 0;
    
    /**
     * @brief 扫描可用的相机设备
     * @param manufacturer 厂商名称（可选，为空则扫描所有厂商）
     * @return 设备信息列表
     */
    virtual std::vector<DeviceInfo> ScanDevices(const std::string& manufacturer = "") = 0;
};

} // namespace VisionModule

#endif // ICAMERAINTERFACE_H
