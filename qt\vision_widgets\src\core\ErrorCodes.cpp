#include "ErrorCodes.h"

namespace VisionModule {

const char* GetErrorDescription(ErrorCode code) {
    switch (code) {
        case ErrorCode::SUCCESS:
            return "操作成功";
            
        // 系统错误 (1000-1099)
        case ErrorCode::SYSTEM_NOT_INITIALIZED:
            return "系统未初始化，请先调用Initialize方法";
        case ErrorCode::INVALID_PARAMETERS:
            return "参数无效，请检查输入参数";
        case ErrorCode::MEMORY_ALLOCATION_FAILED:
            return "内存分配失败，系统内存不足";
        case ErrorCode::FILE_NOT_FOUND:
            return "文件未找到，请检查文件路径";
        case ErrorCode::FILE_ACCESS_DENIED:
            return "文件访问被拒绝，请检查文件权限";
        case ErrorCode::FILE_FORMAT_ERROR:
            return "文件格式错误，不支持的文件格式";
        case ErrorCode::CONFIGURATION_ERROR:
            return "配置错误，请检查配置文件";
        case ErrorCode::LIBRARY_LOAD_FAILED:
            return "库加载失败，请检查依赖库";
        case ErrorCode::LICENSE_ERROR:
            return "许可证错误，请检查Halcon许可证";
        case ErrorCode::TIMEOUT_ERROR:
            return "操作超时，请重试";
            
        // 相机错误 (1100-1199)
        case ErrorCode::CAMERA_NOT_FOUND:
            return "相机未找到，请检查相机连接";
        case ErrorCode::CAMERA_CONNECTION_FAILED:
            return "相机连接失败，请检查相机状态";
        case ErrorCode::CAMERA_DISCONNECTED:
            return "相机已断开连接";
        case ErrorCode::CAMERA_BUSY:
            return "相机忙碌，请稍后重试";
        case ErrorCode::CAMERA_PARAMETER_INVALID:
            return "相机参数无效，请检查参数范围";
        case ErrorCode::CAMERA_CAPTURE_FAILED:
            return "图像采集失败，请检查相机状态";
        case ErrorCode::CAMERA_TIMEOUT:
            return "相机操作超时";
        case ErrorCode::CAMERA_BUFFER_OVERFLOW:
            return "相机缓冲区溢出";
        case ErrorCode::CAMERA_SDK_ERROR:
            return "相机SDK错误";
        case ErrorCode::CAMERA_PERMISSION_DENIED:
            return "相机权限被拒绝";
            
        // 标定错误 (1200-1299)
        case ErrorCode::CALIBRATION_NOT_READY:
            return "标定未就绪，请先完成标定";
        case ErrorCode::CALIBRATION_POINTS_INSUFFICIENT:
            return "标定点不足，至少需要3个标定点";
        case ErrorCode::CALIBRATION_CALCULATION_FAILED:
            return "标定计算失败，请检查标定点";
        case ErrorCode::CALIBRATION_DATA_INVALID:
            return "标定数据无效";
        case ErrorCode::CALIBRATION_ACCURACY_LOW:
            return "标定精度过低，请重新标定";
        case ErrorCode::CALIBRATION_MATRIX_SINGULAR:
            return "标定矩阵奇异，请重新选择标定点";
        case ErrorCode::CALIBRATION_POINTS_COLLINEAR:
            return "标定点共线，请重新选择标定点";
        case ErrorCode::CALIBRATION_SAVE_FAILED:
            return "标定数据保存失败";
        case ErrorCode::CALIBRATION_LOAD_FAILED:
            return "标定数据加载失败";
            
        // 模板匹配错误 (1300-1399)
        case ErrorCode::TEMPLATE_NOT_FOUND:
            return "模板未找到，请先创建或加载模板";
        case ErrorCode::TEMPLATE_CREATION_FAILED:
            return "模板创建失败，请检查ROI区域";
        case ErrorCode::TEMPLATE_MATCHING_FAILED:
            return "模板匹配失败";
        case ErrorCode::TEMPLATE_DATA_INVALID:
            return "模板数据无效";
        case ErrorCode::TEMPLATE_ROI_INVALID:
            return "ROI区域无效，请重新选择";
        case ErrorCode::TEMPLATE_SAVE_FAILED:
            return "模板保存失败";
        case ErrorCode::TEMPLATE_LOAD_FAILED:
            return "模板加载失败";
        case ErrorCode::TEMPLATE_QUALITY_LOW:
            return "模板质量过低，请重新创建";
        case ErrorCode::TEMPLATE_SIZE_INVALID:
            return "模板尺寸无效";
            
        // 坐标转换错误 (1400-1499)
        case ErrorCode::COORDINATE_TRANSFORM_FAILED:
            return "坐标转换失败，请检查标定数据";
        case ErrorCode::CALIBRATION_MATRIX_INVALID:
            return "标定矩阵无效，请重新标定";
        case ErrorCode::COORDINATE_OUT_OF_RANGE:
            return "坐标超出范围";
        case ErrorCode::TRANSFORM_MATRIX_SINGULAR:
            return "变换矩阵奇异";
        case ErrorCode::PIXEL_COORDINATE_INVALID:
            return "像素坐标无效";
        case ErrorCode::PHYSICAL_COORDINATE_INVALID:
            return "物理坐标无效";
            
        // 图像处理错误 (1500-1599)
        case ErrorCode::IMAGE_EMPTY:
            return "图像为空";
        case ErrorCode::IMAGE_FORMAT_UNSUPPORTED:
            return "图像格式不支持";
        case ErrorCode::IMAGE_SIZE_INVALID:
            return "图像尺寸无效";
        case ErrorCode::IMAGE_PROCESSING_FAILED:
            return "图像处理失败";
        case ErrorCode::IMAGE_SAVE_FAILED:
            return "图像保存失败";
        case ErrorCode::IMAGE_LOAD_FAILED:
            return "图像加载失败";
            
        // 算法错误 (1600-1699)
        case ErrorCode::ALGORITHM_NOT_INITIALIZED:
            return "算法未初始化";
        case ErrorCode::ALGORITHM_EXECUTION_FAILED:
            return "算法执行失败";
        case ErrorCode::ALGORITHM_PARAMETER_INVALID:
            return "算法参数无效";
        case ErrorCode::ALGORITHM_CONVERGENCE_FAILED:
            return "算法收敛失败";
            
        // 硬件错误 (1700-1799)
        case ErrorCode::HARDWARE_NOT_FOUND:
            return "硬件未找到";
        case ErrorCode::HARDWARE_INITIALIZATION_FAILED:
            return "硬件初始化失败";
        case ErrorCode::HARDWARE_COMMUNICATION_ERROR:
            return "硬件通信错误";
            
        // 网络错误 (1800-1899)
        case ErrorCode::NETWORK_CONNECTION_FAILED:
            return "网络连接失败";
        case ErrorCode::NETWORK_TIMEOUT:
            return "网络超时";
        case ErrorCode::NETWORK_DATA_CORRUPTED:
            return "网络数据损坏";
            
        case ErrorCode::UNKNOWN_ERROR:
        default:
            return "未知错误";
    }
}

const char* GetErrorCategory(ErrorCode code) {
    int errorValue = static_cast<int>(code);
    
    if (errorValue == 0) {
        return "Success";
    } else if (errorValue >= 1000 && errorValue < 1100) {
        return "System Error";
    } else if (errorValue >= 1100 && errorValue < 1200) {
        return "Camera Error";
    } else if (errorValue >= 1200 && errorValue < 1300) {
        return "Calibration Error";
    } else if (errorValue >= 1300 && errorValue < 1400) {
        return "Template Error";
    } else if (errorValue >= 1400 && errorValue < 1500) {
        return "Coordinate Error";
    } else if (errorValue >= 1500 && errorValue < 1600) {
        return "Image Error";
    } else if (errorValue >= 1600 && errorValue < 1700) {
        return "Algorithm Error";
    } else if (errorValue >= 1700 && errorValue < 1800) {
        return "Hardware Error";
    } else if (errorValue >= 1800 && errorValue < 1900) {
        return "Network Error";
    } else {
        return "Unknown Error";
    }
}

} // namespace VisionModule
