{"AUTOGEN_COMMAND_LINE_LENGTH_MAX": 32000, "BUILD_DIR": "F:/Project/QT_Projact/qt/delta/vison_v4/qt/vision_widgets/build/vision_widgets_autogen", "CMAKE_BINARY_DIR": "F:/Project/QT_Projact/qt/delta/vison_v4/qt/vision_widgets/build", "CMAKE_CURRENT_BINARY_DIR": "F:/Project/QT_Projact/qt/delta/vison_v4/qt/vision_widgets/build", "CMAKE_CURRENT_SOURCE_DIR": "F:/Project/QT_Projact/qt/delta/vison_v4/qt/vision_widgets", "CMAKE_EXECUTABLE": "D:/Qt_5.15/Tools/CMake_64/bin/cmake.exe", "CMAKE_LIST_FILES": ["F:/Project/QT_Projact/qt/delta/vison_v4/qt/vision_widgets/CMakeLists.txt", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineSystem.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeSystem.cmake.in", "F:/Project/QT_Projact/qt/delta/vison_v4/qt/vision_widgets/build/CMakeFiles/3.30.5/CMakeSystem.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeMinGWFindMake.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeSystemSpecificInitialize.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows-Initialize.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompiler.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows-Determine-CXX.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeCompilerIdDetection.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/ADSP-DetermineCompiler.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/ARMCC-DetermineCompiler.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/ARMClang-DetermineCompiler.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/AppleClang-DetermineCompiler.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/Clang-DetermineCompilerInternal.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/Borland-DetermineCompiler.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/Clang-DetermineCompiler.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/Clang-DetermineCompilerInternal.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/Cray-DetermineCompiler.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/CrayClang-DetermineCompiler.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/Embarcadero-DetermineCompiler.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/Fujitsu-DetermineCompiler.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/GHS-DetermineCompiler.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/HP-CXX-DetermineCompiler.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/IAR-DetermineCompiler.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/IBMClang-CXX-DetermineCompiler.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/Intel-DetermineCompiler.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/LCC-CXX-DetermineCompiler.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/MSVC-DetermineCompiler.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/NVHPC-DetermineCompiler.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/NVIDIA-DetermineCompiler.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/OrangeC-DetermineCompiler.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/PGI-DetermineCompiler.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/PathScale-DetermineCompiler.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/SCO-DetermineCompiler.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/TI-DetermineCompiler.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/TIClang-DetermineCompiler.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/Tasking-DetermineCompiler.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/Watcom-DetermineCompiler.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/XL-CXX-DetermineCompiler.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeFindBinUtils.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/GNU-FindBinUtils.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeCXXCompiler.cmake.in", "F:/Project/QT_Projact/qt/delta/vison_v4/qt/vision_widgets/build/CMakeFiles/3.30.5/CMakeCXXCompiler.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeSystemSpecificInformation.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeGenericSystem.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeInitializeConfigs.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Platform/WindowsPaths.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeCXXInformation.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeLanguageInformation.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/GNU-CXX.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/GNU.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/CMakeCommonCompilerMacros.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows-GNU-CXX.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows-GNU.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineRCCompiler.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeRCCompiler.cmake.in", "F:/Project/QT_Projact/qt/delta/vison_v4/qt/vision_widgets/build/CMakeFiles/3.30.5/CMakeRCCompiler.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeRCInformation.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows-windres.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeTestRCCompiler.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeCommonLanguageInclude.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeTestCXXCompiler.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeTestCompilerCommon.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Internal/CMakeDetermineLinkerId.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeParseImplicitIncludeInfo.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeParseImplicitLinkInfo.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeParseLibraryArchitecture.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeTestCompilerCommon.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeCXXCompilerABI.cpp", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerSupport.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Internal/FeatureTesting.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeCXXCompiler.cmake.in", "F:/Project/QT_Projact/qt/delta/vison_v4/qt/vision_widgets/build/CMakeFiles/3.30.5/CMakeCXXCompiler.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows-GNU-CXX-ABI.cmake", "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5/Qt5ConfigVersion.cmake", "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5/Qt5Config.cmake", "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5/Qt5ConfigVersion.cmake", "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5/Qt5Config.cmake", "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5/Qt5ModuleLocation.cmake", "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Widgets/Qt5WidgetsConfigVersion.cmake", "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Widgets/Qt5WidgetsConfig.cmake", "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Gui/Qt5GuiConfigVersion.cmake", "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Gui/Qt5GuiConfig.cmake", "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Core/Qt5CoreConfigVersion.cmake", "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Core/Qt5CoreConfig.cmake", "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Core/Qt5CoreConfigExtras.cmake", "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Core/Qt5CoreConfigExtrasMkspecDir.cmake", "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Core/Qt5CoreMacros.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeParseArguments.cmake", "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QGifPlugin.cmake", "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QICNSPlugin.cmake", "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QICOPlugin.cmake", "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QJpegPlugin.cmake", "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QMinimalIntegrationPlugin.cmake", "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QOffscreenIntegrationPlugin.cmake", "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QSvgIconPlugin.cmake", "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QSvgPlugin.cmake", "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QTgaPlugin.cmake", "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QTiffPlugin.cmake", "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QTuioTouchPlugin.cmake", "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QWbmpPlugin.cmake", "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QWebpPlugin.cmake", "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QWindowsDirect2DIntegrationPlugin.cmake", "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QWindowsIntegrationPlugin.cmake", "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QXdgDesktopPortalThemePlugin.cmake", "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Gui/Qt5GuiConfigExtras.cmake", "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Widgets/Qt5Widgets_QWindowsVistaStylePlugin.cmake", "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Widgets/Qt5WidgetsConfigExtras.cmake", "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Widgets/Qt5WidgetsMacros.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeParseArguments.cmake", "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5LinguistTools/Qt5LinguistToolsConfigVersion.cmake", "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5LinguistTools/Qt5LinguistToolsConfig.cmake", "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5LinguistTools/Qt5LinguistToolsMacros.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeParseArguments.cmake", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/GNUInstallDirs.cmake"], "CMAKE_SOURCE_DIR": "F:/Project/QT_Projact/qt/delta/vison_v4/qt/vision_widgets", "CROSS_CONFIG": false, "DEP_FILE": "F:/Project/QT_Projact/qt/delta/vison_v4/qt/vision_widgets/build/vision_widgets_autogen/deps", "DEP_FILE_RULE_NAME": "vision_widgets_autogen/timestamp", "HEADERS": [["F:/Project/QT_Projact/qt/delta/vison_v4/qt/vision_widgets/widget.h", "MU", "EWIEGA46WW/moc_widget.cpp", null]], "HEADER_EXTENSIONS": ["h", "hh", "h++", "hm", "hpp", "hxx", "in", "txx"], "INCLUDE_DIR": "F:/Project/QT_Projact/qt/delta/vison_v4/qt/vision_widgets/build/vision_widgets_autogen/include", "MOC_COMPILATION_FILE": "F:/Project/QT_Projact/qt/delta/vison_v4/qt/vision_widgets/build/vision_widgets_autogen/mocs_compilation.cpp", "MOC_DEFINITIONS": ["QT_CORE_LIB", "QT_GUI_LIB", "QT_WIDGETS_LIB"], "MOC_DEPEND_FILTERS": [["Q_PLUGIN_METADATA", "[\n][ \t]*Q_PLUGIN_METADATA[ \t]*\\([^\\)]*FILE[ \t]*\"([^\"]+)\""]], "MOC_INCLUDES": ["D:/Qt_5.15/5.15.2/mingw81_64/include", "D:/Qt_5.15/5.15.2/mingw81_64/include/QtWidgets", "D:/Qt_5.15/5.15.2/mingw81_64/include/QtGui", "D:/Qt_5.15/5.15.2/mingw81_64/include/QtANGLE", "D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore", "D:/Qt_5.15/5.15.2/mingw81_64/mkspecs/win32-g++", "D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++", "D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/x86_64-w64-mingw32", "D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/backward", "D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include", "D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include-fixed", "D:/Qt_5.15/Tools/mingw1120_64/x86_64-w64-mingw32/include"], "MOC_MACRO_NAMES": ["Q_OBJECT", "Q_GADGET", "Q_NAMESPACE", "Q_NAMESPACE_EXPORT"], "MOC_OPTIONS": [], "MOC_PATH_PREFIX": false, "MOC_PREDEFS_CMD": ["D:/Qt_5.15/Tools/mingw1120_64/bin/c++.exe", "-std=gnu++17", "-dM", "-E", "-c", "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeCXXCompilerABI.cpp"], "MOC_PREDEFS_FILE": "F:/Project/QT_Projact/qt/delta/vison_v4/qt/vision_widgets/build/vision_widgets_autogen/moc_predefs.h", "MOC_RELAXED_MODE": false, "MOC_SKIP": [], "MULTI_CONFIG": false, "PARALLEL": 10, "PARSE_CACHE_FILE": "F:/Project/QT_Projact/qt/delta/vison_v4/qt/vision_widgets/build/CMakeFiles/vision_widgets_autogen.dir/ParseCache.txt", "QT_MOC_EXECUTABLE": "D:/Qt_5.15/5.15.2/mingw81_64/bin/moc.exe", "QT_UIC_EXECUTABLE": "D:/Qt_5.15/5.15.2/mingw81_64/bin/uic.exe", "QT_VERSION_MAJOR": 5, "QT_VERSION_MINOR": 15, "SETTINGS_FILE": "F:/Project/QT_Projact/qt/delta/vison_v4/qt/vision_widgets/build/CMakeFiles/vision_widgets_autogen.dir/AutogenUsed.txt", "SOURCES": [["F:/Project/QT_Projact/qt/delta/vison_v4/qt/vision_widgets/main.cpp", "MU", null], ["F:/Project/QT_Projact/qt/delta/vison_v4/qt/vision_widgets/widget.cpp", "MU", null]], "UIC_OPTIONS": [], "UIC_SEARCH_PATHS": [], "UIC_SKIP": [], "UIC_UI_FILES": [], "USE_BETTER_GRAPH": false, "VERBOSITY": 0}