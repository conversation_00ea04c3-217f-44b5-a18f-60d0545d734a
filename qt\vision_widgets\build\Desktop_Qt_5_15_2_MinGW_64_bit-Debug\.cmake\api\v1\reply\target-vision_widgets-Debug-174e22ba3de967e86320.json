{"artifacts": [{"path": "vision_widgets.exe"}, {"path": "vision_widgets.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "install", "target_link_libraries", "set_property", "_populate_Widgets_target_properties", "find_package", "include"], "files": ["CMakeLists.txt", "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Widgets/Qt5WidgetsConfig.cmake", "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5/Qt5Config.cmake", "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Core/Qt5CoreConfigExtras.cmake", "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Core/Qt5CoreConfig.cmake", "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Gui/Qt5GuiConfig.cmake"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 44, "parent": 0}, {"command": 1, "file": 0, "line": 69, "parent": 0}, {"command": 2, "file": 0, "line": 52, "parent": 0}, {"command": 5, "file": 0, "line": 13, "parent": 0}, {"file": 2, "parent": 4}, {"command": 5, "file": 2, "line": 28, "parent": 5}, {"file": 1, "parent": 6}, {"command": 4, "file": 1, "line": 207, "parent": 7}, {"command": 3, "file": 1, "line": 44, "parent": 8}, {"command": 5, "file": 1, "line": 105, "parent": 7}, {"file": 5, "parent": 10}, {"command": 5, "file": 5, "line": 105, "parent": 11}, {"file": 4, "parent": 12}, {"command": 6, "file": 4, "line": 244, "parent": 13}, {"file": 3, "parent": 14}, {"command": 3, "file": 3, "line": 106, "parent": 15}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-DQT_QML_DEBUG -g -std=gnu++17 -fdiagnostics-color=always"}], "defines": [{"backtrace": 3, "define": "QT_CORE_LIB"}, {"backtrace": 3, "define": "QT_GUI_LIB"}, {"backtrace": 3, "define": "QT_WIDGETS_LIB"}], "includes": [{"backtrace": 0, "path": "F:/Project/QT_Projact/qt/delta/vison_v4/qt/vision_widgets/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/vision_widgets_autogen/include"}, {"backtrace": 3, "isSystem": true, "path": "D:/Qt_5.15/5.15.2/mingw81_64/include"}, {"backtrace": 3, "isSystem": true, "path": "D:/Qt_5.15/5.15.2/mingw81_64/include/QtWidgets"}, {"backtrace": 3, "isSystem": true, "path": "D:/Qt_5.15/5.15.2/mingw81_64/include/QtGui"}, {"backtrace": 3, "isSystem": true, "path": "D:/Qt_5.15/5.15.2/mingw81_64/include/QtANGLE"}, {"backtrace": 3, "isSystem": true, "path": "D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore"}, {"backtrace": 3, "isSystem": true, "path": "D:/Qt_5.15/5.15.2/mingw81_64/./mkspecs/win32-g++"}], "language": "CXX", "languageStandard": {"backtraces": [3], "standard": "17"}, "sourceIndexes": [0, 1, 2]}], "dependencies": [{"id": "vision_widgets_autogen_timestamp_deps::@6890427a1f51a3e7e1df"}, {"backtrace": 0, "id": "vision_widgets_autogen::@6890427a1f51a3e7e1df"}], "id": "vision_widgets::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 2, "path": "bin"}], "prefix": {"path": "C:/Program Files (x86)/vision_widgets"}}, "link": {"commandFragments": [{"fragment": "-DQT_QML_DEBUG -g", "role": "flags"}, {"fragment": "-mwindows", "role": "flags"}, {"backtrace": 3, "fragment": "D:\\Qt_5.15\\5.15.2\\mingw81_64\\lib\\libQt5Widgets.a", "role": "libraries"}, {"backtrace": 9, "fragment": "D:\\Qt_5.15\\5.15.2\\mingw81_64\\lib\\libQt5Gui.a", "role": "libraries"}, {"backtrace": 9, "fragment": "D:\\Qt_5.15\\5.15.2\\mingw81_64\\lib\\libQt5Core.a", "role": "libraries"}, {"backtrace": 16, "fragment": "D:\\Qt_5.15\\5.15.2\\mingw81_64\\lib\\libqtmain.a", "role": "libraries"}, {"fragment": "-lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32", "role": "libraries"}], "language": "CXX"}, "name": "vision_widgets", "nameOnDisk": "vision_widgets.exe", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [3, 6]}, {"name": "Forms", "sourceIndexes": [4]}, {"name": "", "sourceIndexes": [5, 7]}, {"name": "CMake Rules", "sourceIndexes": [8, 9]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/vision_widgets_autogen/mocs_compilation.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "main.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "widget.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "widget.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "widget.ui", "sourceGroupIndex": 2}, {"backtrace": 1, "isGenerated": true, "path": "vision_widgets_zh_CN.ts", "sourceGroupIndex": 3}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/vision_widgets_autogen/include/ui_widget.h", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/vision_widgets_autogen/timestamp", "sourceGroupIndex": 3}, {"backtrace": 0, "isGenerated": true, "path": "vision_widgets_zh_CN.ts.rule", "sourceGroupIndex": 4}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/vision_widgets_autogen/timestamp.rule", "sourceGroupIndex": 4}], "type": "EXECUTABLE"}