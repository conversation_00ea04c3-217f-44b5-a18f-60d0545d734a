# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.30

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = D:\Qt_5.15\Tools\CMake_64\bin\cmake.exe

# The command to remove a file.
RM = D:\Qt_5.15\Tools\CMake_64\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = F:\Project\QT_Projact\qt\delta\vison_v4\qt\vision_widgets

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = F:\Project\QT_Projact\qt\delta\vison_v4\qt\vision_widgets\build\Desktop_Qt_5_15_2_MinGW_64_bit-Debug

# Utility rule file for vision_widgets_autogen.

# Include any custom commands dependencies for this target.
include CMakeFiles/vision_widgets_autogen.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/vision_widgets_autogen.dir/progress.make

CMakeFiles/vision_widgets_autogen: vision_widgets_autogen/timestamp

vision_widgets_autogen/timestamp: D:/Qt_5.15/5.15.2/mingw81_64/bin/moc.exe
vision_widgets_autogen/timestamp: D:/Qt_5.15/5.15.2/mingw81_64/bin/uic.exe
vision_widgets_autogen/timestamp: CMakeFiles/vision_widgets_autogen.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=F:\Project\QT_Projact\qt\delta\vison_v4\qt\vision_widgets\build\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Automatic MOC and UIC for target vision_widgets"
	D:\Qt_5.15\Tools\CMake_64\bin\cmake.exe -E cmake_autogen F:/Project/QT_Projact/qt/delta/vison_v4/qt/vision_widgets/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/CMakeFiles/vision_widgets_autogen.dir/AutogenInfo.json Debug
	D:\Qt_5.15\Tools\CMake_64\bin\cmake.exe -E touch F:/Project/QT_Projact/qt/delta/vison_v4/qt/vision_widgets/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/vision_widgets_autogen/timestamp

vision_widgets_autogen: CMakeFiles/vision_widgets_autogen
vision_widgets_autogen: vision_widgets_autogen/timestamp
vision_widgets_autogen: CMakeFiles/vision_widgets_autogen.dir/build.make
.PHONY : vision_widgets_autogen

# Rule to build all files generated by this target.
CMakeFiles/vision_widgets_autogen.dir/build: vision_widgets_autogen
.PHONY : CMakeFiles/vision_widgets_autogen.dir/build

CMakeFiles/vision_widgets_autogen.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles\vision_widgets_autogen.dir\cmake_clean.cmake
.PHONY : CMakeFiles/vision_widgets_autogen.dir/clean

CMakeFiles/vision_widgets_autogen.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" F:\Project\QT_Projact\qt\delta\vison_v4\qt\vision_widgets F:\Project\QT_Projact\qt\delta\vison_v4\qt\vision_widgets F:\Project\QT_Projact\qt\delta\vison_v4\qt\vision_widgets\build\Desktop_Qt_5_15_2_MinGW_64_bit-Debug F:\Project\QT_Projact\qt\delta\vison_v4\qt\vision_widgets\build\Desktop_Qt_5_15_2_MinGW_64_bit-Debug F:\Project\QT_Projact\qt\delta\vison_v4\qt\vision_widgets\build\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\CMakeFiles\vision_widgets_autogen.dir\DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/vision_widgets_autogen.dir/depend

