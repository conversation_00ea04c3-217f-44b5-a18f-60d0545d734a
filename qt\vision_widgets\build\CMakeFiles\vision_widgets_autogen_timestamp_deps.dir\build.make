# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.30

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = D:\Qt_5.15\Tools\CMake_64\bin\cmake.exe

# The command to remove a file.
RM = D:\Qt_5.15\Tools\CMake_64\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = F:\Project\QT_Projact\qt\delta\vison_v4\qt\vision_widgets

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = F:\Project\QT_Projact\qt\delta\vison_v4\qt\vision_widgets\build

# Utility rule file for vision_widgets_autogen_timestamp_deps.

# Include any custom commands dependencies for this target.
include CMakeFiles/vision_widgets_autogen_timestamp_deps.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/vision_widgets_autogen_timestamp_deps.dir/progress.make

vision_widgets_autogen_timestamp_deps: CMakeFiles/vision_widgets_autogen_timestamp_deps.dir/build.make
.PHONY : vision_widgets_autogen_timestamp_deps

# Rule to build all files generated by this target.
CMakeFiles/vision_widgets_autogen_timestamp_deps.dir/build: vision_widgets_autogen_timestamp_deps
.PHONY : CMakeFiles/vision_widgets_autogen_timestamp_deps.dir/build

CMakeFiles/vision_widgets_autogen_timestamp_deps.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles\vision_widgets_autogen_timestamp_deps.dir\cmake_clean.cmake
.PHONY : CMakeFiles/vision_widgets_autogen_timestamp_deps.dir/clean

CMakeFiles/vision_widgets_autogen_timestamp_deps.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" F:\Project\QT_Projact\qt\delta\vison_v4\qt\vision_widgets F:\Project\QT_Projact\qt\delta\vison_v4\qt\vision_widgets F:\Project\QT_Projact\qt\delta\vison_v4\qt\vision_widgets\build F:\Project\QT_Projact\qt\delta\vison_v4\qt\vision_widgets\build F:\Project\QT_Projact\qt\delta\vison_v4\qt\vision_widgets\build\CMakeFiles\vision_widgets_autogen_timestamp_deps.dir\DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/vision_widgets_autogen_timestamp_deps.dir/depend

