# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.30

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = D:\Qt_5.15\Tools\CMake_64\bin\cmake.exe

# The command to remove a file.
RM = D:\Qt_5.15\Tools\CMake_64\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = F:\Project\QT_Projact\qt\delta\vison_v4\qt\vision_widgets

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = F:\Project\QT_Projact\qt\delta\vison_v4\qt\vision_widgets\build

# Include any dependencies generated for this target.
include CMakeFiles/vision_widgets.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/vision_widgets.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/vision_widgets.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/vision_widgets.dir/flags.make

F:/Project/QT_Projact/qt/delta/vison_v4/qt/vision_widgets/vision_widgets_zh_CN.ts: F:/Project/QT_Projact/qt/delta/vison_v4/qt/vision_widgets
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=F:\Project\QT_Projact\qt\delta\vison_v4\qt\vision_widgets\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Generating F:/Project/QT_Projact/qt/delta/vison_v4/qt/vision_widgets/vision_widgets_zh_CN.ts"
	D:\Qt_5.15\5.15.2\mingw81_64\bin\lupdate.exe @F:/Project/QT_Projact/qt/delta/vison_v4/qt/vision_widgets/build/CMakeFiles/vision_widgets_zh_CN.ts_lst_file -ts F:/Project/QT_Projact/qt/delta/vison_v4/qt/vision_widgets/vision_widgets_zh_CN.ts

vision_widgets_autogen/timestamp: D:/Qt_5.15/5.15.2/mingw81_64/bin/moc.exe
vision_widgets_autogen/timestamp: D:/Qt_5.15/5.15.2/mingw81_64/bin/uic.exe
vision_widgets_autogen/timestamp: CMakeFiles/vision_widgets.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=F:\Project\QT_Projact\qt\delta\vison_v4\qt\vision_widgets\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Automatic MOC and UIC for target vision_widgets"
	D:\Qt_5.15\Tools\CMake_64\bin\cmake.exe -E cmake_autogen F:/Project/QT_Projact/qt/delta/vison_v4/qt/vision_widgets/build/CMakeFiles/vision_widgets_autogen.dir/AutogenInfo.json Debug
	D:\Qt_5.15\Tools\CMake_64\bin\cmake.exe -E touch F:/Project/QT_Projact/qt/delta/vison_v4/qt/vision_widgets/build/vision_widgets_autogen/timestamp

CMakeFiles/vision_widgets.dir/vision_widgets_autogen/mocs_compilation.cpp.obj: CMakeFiles/vision_widgets.dir/flags.make
CMakeFiles/vision_widgets.dir/vision_widgets_autogen/mocs_compilation.cpp.obj: CMakeFiles/vision_widgets.dir/includes_CXX.rsp
CMakeFiles/vision_widgets.dir/vision_widgets_autogen/mocs_compilation.cpp.obj: vision_widgets_autogen/mocs_compilation.cpp
CMakeFiles/vision_widgets.dir/vision_widgets_autogen/mocs_compilation.cpp.obj: CMakeFiles/vision_widgets.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=F:\Project\QT_Projact\qt\delta\vison_v4\qt\vision_widgets\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/vision_widgets.dir/vision_widgets_autogen/mocs_compilation.cpp.obj"
	D:\Qt_5.15\Tools\mingw1120_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/vision_widgets.dir/vision_widgets_autogen/mocs_compilation.cpp.obj -MF CMakeFiles\vision_widgets.dir\vision_widgets_autogen\mocs_compilation.cpp.obj.d -o CMakeFiles\vision_widgets.dir\vision_widgets_autogen\mocs_compilation.cpp.obj -c F:\Project\QT_Projact\qt\delta\vison_v4\qt\vision_widgets\build\vision_widgets_autogen\mocs_compilation.cpp

CMakeFiles/vision_widgets.dir/vision_widgets_autogen/mocs_compilation.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/vision_widgets.dir/vision_widgets_autogen/mocs_compilation.cpp.i"
	D:\Qt_5.15\Tools\mingw1120_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E F:\Project\QT_Projact\qt\delta\vison_v4\qt\vision_widgets\build\vision_widgets_autogen\mocs_compilation.cpp > CMakeFiles\vision_widgets.dir\vision_widgets_autogen\mocs_compilation.cpp.i

CMakeFiles/vision_widgets.dir/vision_widgets_autogen/mocs_compilation.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/vision_widgets.dir/vision_widgets_autogen/mocs_compilation.cpp.s"
	D:\Qt_5.15\Tools\mingw1120_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S F:\Project\QT_Projact\qt\delta\vison_v4\qt\vision_widgets\build\vision_widgets_autogen\mocs_compilation.cpp -o CMakeFiles\vision_widgets.dir\vision_widgets_autogen\mocs_compilation.cpp.s

CMakeFiles/vision_widgets.dir/main.cpp.obj: CMakeFiles/vision_widgets.dir/flags.make
CMakeFiles/vision_widgets.dir/main.cpp.obj: CMakeFiles/vision_widgets.dir/includes_CXX.rsp
CMakeFiles/vision_widgets.dir/main.cpp.obj: F:/Project/QT_Projact/qt/delta/vison_v4/qt/vision_widgets/main.cpp
CMakeFiles/vision_widgets.dir/main.cpp.obj: CMakeFiles/vision_widgets.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=F:\Project\QT_Projact\qt\delta\vison_v4\qt\vision_widgets\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/vision_widgets.dir/main.cpp.obj"
	D:\Qt_5.15\Tools\mingw1120_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/vision_widgets.dir/main.cpp.obj -MF CMakeFiles\vision_widgets.dir\main.cpp.obj.d -o CMakeFiles\vision_widgets.dir\main.cpp.obj -c F:\Project\QT_Projact\qt\delta\vison_v4\qt\vision_widgets\main.cpp

CMakeFiles/vision_widgets.dir/main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/vision_widgets.dir/main.cpp.i"
	D:\Qt_5.15\Tools\mingw1120_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E F:\Project\QT_Projact\qt\delta\vison_v4\qt\vision_widgets\main.cpp > CMakeFiles\vision_widgets.dir\main.cpp.i

CMakeFiles/vision_widgets.dir/main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/vision_widgets.dir/main.cpp.s"
	D:\Qt_5.15\Tools\mingw1120_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S F:\Project\QT_Projact\qt\delta\vison_v4\qt\vision_widgets\main.cpp -o CMakeFiles\vision_widgets.dir\main.cpp.s

CMakeFiles/vision_widgets.dir/widget.cpp.obj: CMakeFiles/vision_widgets.dir/flags.make
CMakeFiles/vision_widgets.dir/widget.cpp.obj: CMakeFiles/vision_widgets.dir/includes_CXX.rsp
CMakeFiles/vision_widgets.dir/widget.cpp.obj: F:/Project/QT_Projact/qt/delta/vison_v4/qt/vision_widgets/widget.cpp
CMakeFiles/vision_widgets.dir/widget.cpp.obj: CMakeFiles/vision_widgets.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=F:\Project\QT_Projact\qt\delta\vison_v4\qt\vision_widgets\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object CMakeFiles/vision_widgets.dir/widget.cpp.obj"
	D:\Qt_5.15\Tools\mingw1120_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/vision_widgets.dir/widget.cpp.obj -MF CMakeFiles\vision_widgets.dir\widget.cpp.obj.d -o CMakeFiles\vision_widgets.dir\widget.cpp.obj -c F:\Project\QT_Projact\qt\delta\vison_v4\qt\vision_widgets\widget.cpp

CMakeFiles/vision_widgets.dir/widget.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/vision_widgets.dir/widget.cpp.i"
	D:\Qt_5.15\Tools\mingw1120_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E F:\Project\QT_Projact\qt\delta\vison_v4\qt\vision_widgets\widget.cpp > CMakeFiles\vision_widgets.dir\widget.cpp.i

CMakeFiles/vision_widgets.dir/widget.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/vision_widgets.dir/widget.cpp.s"
	D:\Qt_5.15\Tools\mingw1120_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S F:\Project\QT_Projact\qt\delta\vison_v4\qt\vision_widgets\widget.cpp -o CMakeFiles\vision_widgets.dir\widget.cpp.s

# Object files for target vision_widgets
vision_widgets_OBJECTS = \
"CMakeFiles/vision_widgets.dir/vision_widgets_autogen/mocs_compilation.cpp.obj" \
"CMakeFiles/vision_widgets.dir/main.cpp.obj" \
"CMakeFiles/vision_widgets.dir/widget.cpp.obj"

# External object files for target vision_widgets
vision_widgets_EXTERNAL_OBJECTS =

vision_widgets.exe: CMakeFiles/vision_widgets.dir/vision_widgets_autogen/mocs_compilation.cpp.obj
vision_widgets.exe: CMakeFiles/vision_widgets.dir/main.cpp.obj
vision_widgets.exe: CMakeFiles/vision_widgets.dir/widget.cpp.obj
vision_widgets.exe: CMakeFiles/vision_widgets.dir/build.make
vision_widgets.exe: D:/Qt_5.15/5.15.2/mingw81_64/lib/libQt5Widgets.a
vision_widgets.exe: D:/Qt_5.15/5.15.2/mingw81_64/lib/libQt5Gui.a
vision_widgets.exe: D:/Qt_5.15/5.15.2/mingw81_64/lib/libQt5Core.a
vision_widgets.exe: D:/Qt_5.15/5.15.2/mingw81_64/lib/libqtmain.a
vision_widgets.exe: CMakeFiles/vision_widgets.dir/linkLibs.rsp
vision_widgets.exe: CMakeFiles/vision_widgets.dir/objects1.rsp
vision_widgets.exe: CMakeFiles/vision_widgets.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=F:\Project\QT_Projact\qt\delta\vison_v4\qt\vision_widgets\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Linking CXX executable vision_widgets.exe"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\vision_widgets.dir\link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/vision_widgets.dir/build: vision_widgets.exe
.PHONY : CMakeFiles/vision_widgets.dir/build

CMakeFiles/vision_widgets.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles\vision_widgets.dir\cmake_clean.cmake
.PHONY : CMakeFiles/vision_widgets.dir/clean

CMakeFiles/vision_widgets.dir/depend: vision_widgets_autogen/timestamp
CMakeFiles/vision_widgets.dir/depend: F:/Project/QT_Projact/qt/delta/vison_v4/qt/vision_widgets/vision_widgets_zh_CN.ts
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" F:\Project\QT_Projact\qt\delta\vison_v4\qt\vision_widgets F:\Project\QT_Projact\qt\delta\vison_v4\qt\vision_widgets F:\Project\QT_Projact\qt\delta\vison_v4\qt\vision_widgets\build F:\Project\QT_Projact\qt\delta\vison_v4\qt\vision_widgets\build F:\Project\QT_Projact\qt\delta\vison_v4\qt\vision_widgets\build\CMakeFiles\vision_widgets.dir\DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/vision_widgets.dir/depend

