#ifndef CAMERACONTROLLER_H
#define CAMERACONTROLLER_H

/**
 * @file CameraController.h
 * @brief 相机控制器主类
 * <AUTHOR> Module Team
 * @version 1.0.0
 * @date 2025-07-04
 */

#include <memory>
#include <vector>
#include <string>
#include <functional>
#include <mutex>
#include "ICameraInterface.h"
#include "../core/VisionTypes.h"
#include "../core/ErrorCodes.h"
#include "../core/IVisionCallback.h"

namespace VisionModule {

/**
 * @brief 相机控制器主类
 * 
 * 提供统一的相机管理接口，支持多厂商相机的自动识别和控制。
 * 使用工厂模式创建不同厂商的相机实例，提供高级的相机操作功能。
 */
class CameraController {
public:
    CameraController();
    ~CameraController();
    
    // 禁用拷贝构造和赋值
    CameraController(const CameraController&) = delete;
    CameraController& operator=(const CameraController&) = delete;
    
    /**
     * @brief 初始化相机控制器
     * @return 错误码
     */
    ErrorCode Initialize();
    
    /**
     * @brief 清理相机控制器
     * @return 错误码
     */
    ErrorCode Finalize();
    
    /**
     * @brief 检查是否已初始化
     * @return true-已初始化，false-未初始化
     */
    bool IsInitialized() const;
    
    /**
     * @brief 扫描可用的相机设备
     * @param manufacturer 厂商名称（可选，为空则扫描所有厂商）
     * @return 设备信息列表
     */
    std::vector<DeviceInfo> ScanDevices(const std::string& manufacturer = "");
    
    /**
     * @brief 连接相机
     * @param deviceId 设备ID
     * @param manufacturer 厂商名称（可选，自动识别）
     * @return 错误码
     */
    ErrorCode ConnectCamera(const std::string& deviceId, const std::string& manufacturer = "");
    
    /**
     * @brief 断开相机连接
     * @return 错误码
     */
    ErrorCode DisconnectCamera();
    
    /**
     * @brief 检查相机是否已连接
     * @return true-已连接，false-未连接
     */
    bool IsCameraConnected() const;
    
    /**
     * @brief 获取当前相机信息
     * @param info 输出设备信息
     * @return 错误码
     */
    ErrorCode GetCurrentCameraInfo(DeviceInfo& info) const;
    
    /**
     * @brief 单次图像采集
     * @param image 输出图像
     * @return 错误码
     */
    ErrorCode CaptureImage(cv::Mat& image);
    
    /**
     * @brief 开始连续采集
     * @param callback 图像回调函数
     * @return 错误码
     */
    ErrorCode StartContinuousCapture(std::function<void(const cv::Mat&, uint64_t)> callback);
    
    /**
     * @brief 停止连续采集
     * @return 错误码
     */
    ErrorCode StopContinuousCapture();
    
    /**
     * @brief 检查是否正在连续采集
     * @return true-正在采集，false-未采集
     */
    bool IsCapturing() const;
    
    /**
     * @brief 设置相机参数
     * @param parameters 相机参数
     * @return 错误码
     */
    ErrorCode SetCameraParameters(const CameraParameters& parameters);
    
    /**
     * @brief 获取相机参数
     * @param parameters 输出相机参数
     * @return 错误码
     */
    ErrorCode GetCameraParameters(CameraParameters& parameters) const;
    
    /**
     * @brief 设置曝光时间
     * @param exposure 曝光时间（毫秒）
     * @return 错误码
     */
    ErrorCode SetExposure(double exposure);
    
    /**
     * @brief 获取曝光时间
     * @param exposure 输出曝光时间（毫秒）
     * @return 错误码
     */
    ErrorCode GetExposure(double& exposure) const;
    
    /**
     * @brief 设置增益
     * @param gain 增益值
     * @return 错误码
     */
    ErrorCode SetGain(double gain);
    
    /**
     * @brief 获取增益
     * @param gain 输出增益值
     * @return 错误码
     */
    ErrorCode GetGain(double& gain) const;
    
    /**
     * @brief 设置图像尺寸
     * @param width 图像宽度
     * @param height 图像高度
     * @return 错误码
     */
    ErrorCode SetImageSize(int width, int height);
    
    /**
     * @brief 获取图像尺寸
     * @param width 输出图像宽度
     * @param height 输出图像高度
     * @return 错误码
     */
    ErrorCode GetImageSize(int& width, int& height) const;
    
    /**
     * @brief 设置帧率
     * @param frameRate 帧率
     * @return 错误码
     */
    ErrorCode SetFrameRate(double frameRate);
    
    /**
     * @brief 获取帧率
     * @param frameRate 输出帧率
     * @return 错误码
     */
    ErrorCode GetFrameRate(double& frameRate) const;
    
    /**
     * @brief 设置触发模式
     * @param triggerMode 触发模式
     * @return 错误码
     */
    ErrorCode SetTriggerMode(const std::string& triggerMode);
    
    /**
     * @brief 获取触发模式
     * @param triggerMode 输出触发模式
     * @return 错误码
     */
    ErrorCode GetTriggerMode(std::string& triggerMode) const;
    
    /**
     * @brief 软件触发
     * @return 错误码
     */
    ErrorCode SoftwareTrigger();
    
    /**
     * @brief 获取参数范围
     * @param paramName 参数名称
     * @param minValue 输出最小值
     * @param maxValue 输出最大值
     * @return 错误码
     */
    ErrorCode GetParameterRange(const std::string& paramName, double& minValue, double& maxValue) const;
    
    /**
     * @brief 重置相机参数为默认值
     * @return 错误码
     */
    ErrorCode ResetToDefault();
    
    /**
     * @brief 保存相机配置
     * @param filePath 配置文件路径
     * @return 错误码
     */
    ErrorCode SaveConfiguration(const std::string& filePath);
    
    /**
     * @brief 加载相机配置
     * @param filePath 配置文件路径
     * @return 错误码
     */
    ErrorCode LoadConfiguration(const std::string& filePath);
    
    /**
     * @brief 获取相机状态
     * @return 相机状态
     */
    CameraStatus GetCameraStatus() const;
    
    /**
     * @brief 获取支持的厂商列表
     * @return 厂商名称列表
     */
    std::vector<std::string> GetSupportedManufacturers() const;
    
    /**
     * @brief 检查相机是否支持某个功能
     * @param feature 功能名称
     * @return true-支持，false-不支持
     */
    bool IsFeatureSupported(const std::string& feature) const;
    
    /**
     * @brief 获取最后一次错误信息
     * @return 错误描述字符串
     */
    std::string GetLastError() const;
    
    /**
     * @brief 设置回调管理器
     * @param callbackManager 回调管理器指针
     */
    void SetCallbackManager(std::shared_ptr<CallbackManager> callbackManager);
    
    /**
     * @brief 设置错误回调函数
     * @param callback 错误回调函数
     */
    void SetErrorCallback(std::function<void(ErrorCode, const std::string&)> callback);
    
    /**
     * @brief 设置状态变化回调函数
     * @param callback 状态变化回调函数
     */
    void SetStatusCallback(std::function<void(CameraStatus)> callback);
    
    /**
     * @brief 获取当前相机厂商
     * @return 厂商名称
     */
    std::string GetCurrentManufacturer() const;
    
    /**
     * @brief 获取当前相机型号
     * @return 相机型号
     */
    std::string GetCurrentModel() const;

private:
    // 内部实现类（PIMPL模式）
    class Impl;
    std::unique_ptr<Impl> m_pImpl;
    
    // 状态管理
    mutable std::mutex m_mutex;
    bool m_initialized;
    std::string m_lastError;
    
    // 回调管理
    std::shared_ptr<CallbackManager> m_callbackManager;
    std::function<void(ErrorCode, const std::string&)> m_errorCallback;
    std::function<void(CameraStatus)> m_statusCallback;
    
    /**
     * @brief 设置错误信息
     * @param error 错误码
     * @param message 错误消息
     */
    void SetError(ErrorCode error, const std::string& message);
    
    /**
     * @brief 自动识别相机厂商
     * @param deviceId 设备ID
     * @return 厂商名称
     */
    std::string AutoDetectManufacturer(const std::string& deviceId) const;
    
    /**
     * @brief 创建相机工厂
     * @param manufacturer 厂商名称
     * @return 相机工厂指针
     */
    std::unique_ptr<ICameraFactory> CreateCameraFactory(const std::string& manufacturer) const;
    
    /**
     * @brief 验证相机是否可用
     * @return 错误码
     */
    ErrorCode ValidateCamera() const;
};

} // namespace VisionModule

#endif // CAMERACONTROLLER_H
