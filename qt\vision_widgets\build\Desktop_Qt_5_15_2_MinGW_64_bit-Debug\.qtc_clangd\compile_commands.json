[{"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\Qt_5.15\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\Qt_5.15\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\Qt_5.15\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IF:\\Project\\QT_Projact\\qt\\delta\\vison_v4\\qt\\vision_widgets\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\vision_widgets_autogen\\include", "-isystem", "D:\\Qt_5.15\\5.15.2\\mingw81_64\\include", "-isystem", "D:\\Qt_5.15\\5.15.2\\mingw81_64\\include\\QtWidgets", "-isystem", "D:\\Qt_5.15\\5.15.2\\mingw81_64\\include\\QtGui", "-isystem", "D:\\Qt_5.15\\5.15.2\\mingw81_64\\include\\QtANGLE", "-isystem", "D:\\Qt_5.15\\5.15.2\\mingw81_64\\include\\QtCore", "-isystem", "D:\\Qt_5.15\\5.15.2\\mingw81_64\\mkspecs\\win32-g++", "-isystem", "D:\\Qt_5.15\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "D:\\Qt_5.15\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\Qt_5.15\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "D:\\Qt_5.15\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\Qt_5.15\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "F:\\Project\\QT_Projact\\qt\\delta\\vison_v4\\qt\\vision_widgets\\main.cpp"], "directory": "F:/Project/QT_Projact/qt/delta/vison_v4/qt/vision_widgets/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "F:/Project/QT_Projact/qt/delta/vison_v4/qt/vision_widgets/main.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\Qt_5.15\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\Qt_5.15\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\Qt_5.15\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IF:\\Project\\QT_Projact\\qt\\delta\\vison_v4\\qt\\vision_widgets\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\vision_widgets_autogen\\include", "-isystem", "D:\\Qt_5.15\\5.15.2\\mingw81_64\\include", "-isystem", "D:\\Qt_5.15\\5.15.2\\mingw81_64\\include\\QtWidgets", "-isystem", "D:\\Qt_5.15\\5.15.2\\mingw81_64\\include\\QtGui", "-isystem", "D:\\Qt_5.15\\5.15.2\\mingw81_64\\include\\QtANGLE", "-isystem", "D:\\Qt_5.15\\5.15.2\\mingw81_64\\include\\QtCore", "-isystem", "D:\\Qt_5.15\\5.15.2\\mingw81_64\\mkspecs\\win32-g++", "-isystem", "D:\\Qt_5.15\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "D:\\Qt_5.15\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\Qt_5.15\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "D:\\Qt_5.15\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\Qt_5.15\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "F:\\Project\\QT_Projact\\qt\\delta\\vison_v4\\qt\\vision_widgets\\widget.cpp"], "directory": "F:/Project/QT_Projact/qt/delta/vison_v4/qt/vision_widgets/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "F:/Project/QT_Projact/qt/delta/vison_v4/qt/vision_widgets/widget.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\Qt_5.15\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\Qt_5.15\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\Qt_5.15\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IF:\\Project\\QT_Projact\\qt\\delta\\vison_v4\\qt\\vision_widgets\\build\\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\vision_widgets_autogen\\include", "-isystem", "D:\\Qt_5.15\\5.15.2\\mingw81_64\\include", "-isystem", "D:\\Qt_5.15\\5.15.2\\mingw81_64\\include\\QtWidgets", "-isystem", "D:\\Qt_5.15\\5.15.2\\mingw81_64\\include\\QtGui", "-isystem", "D:\\Qt_5.15\\5.15.2\\mingw81_64\\include\\QtANGLE", "-isystem", "D:\\Qt_5.15\\5.15.2\\mingw81_64\\include\\QtCore", "-isystem", "D:\\Qt_5.15\\5.15.2\\mingw81_64\\mkspecs\\win32-g++", "-isystem", "D:\\Qt_5.15\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "D:\\Qt_5.15\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\Qt_5.15\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "D:\\Qt_5.15\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\Qt_5.15\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "F:\\Project\\QT_Projact\\qt\\delta\\vison_v4\\qt\\vision_widgets\\widget.h"], "directory": "F:/Project/QT_Projact/qt/delta/vison_v4/qt/vision_widgets/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/.qtc_clangd", "file": "F:/Project/QT_Projact/qt/delta/vison_v4/qt/vision_widgets/widget.h"}]