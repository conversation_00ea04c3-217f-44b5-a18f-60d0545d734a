#include "VisionTypes.h"
#include <cmath>
#include <sstream>
#include <iomanip>

namespace VisionModule {

// Point2D 实现
double Point2D::distanceTo(const Point2D& other) const {
    double dx = x - other.x;
    double dy = y - other.y;
    return std::sqrt(dx * dx + dy * dy);
}

// Coordinate2D 实现
std::string Coordinate2D::toString() const {
    std::ostringstream oss;
    oss << std::fixed << std::setprecision(3);
    oss << "(" << x << ", " << y << ", " << angle << "°, conf:" << confidence << ")";
    return oss.str();
}

// Rectangle 实现
bool Rectangle::contains(const Point2D& point) const {
    return point.x >= x && point.x < (x + width) &&
           point.y >= y && point.y < (y + height);
}

bool Rectangle::intersects(const Rectangle& other) const {
    return !(x >= other.x + other.width ||
             other.x >= x + width ||
             y >= other.y + other.height ||
             other.y >= y + height);
}

// CameraParameters 实现
bool CameraParameters::isValid() const {
    return exposure > 0.0 && exposure <= 1000.0 &&
           gain >= 1.0 && gain <= 50.0 &&
           width > 0 && height > 0 &&
           frameRate > 0.0 && frameRate <= 120.0;
}

std::string CameraParameters::toString() const {
    std::ostringstream oss;
    oss << "Camera[" << width << "x" << height << ", "
        << "exp:" << exposure << "ms, "
        << "gain:" << gain << ", "
        << "fps:" << frameRate << ", "
        << "fmt:" << format << "]";
    return oss.str();
}

// MatchingParameters 实现
bool MatchingParameters::isValid() const {
    return threshold >= 0.0 && threshold <= 1.0 &&
           maxMatches > 0 && maxMatches <= 100 &&
           angleRange >= 0.0 && angleRange <= 360.0 &&
           scaleMin > 0.0 && scaleMin <= scaleMax &&
           scaleMax >= scaleMin && scaleMax <= 5.0 &&
           pyramidLevels >= 1 && pyramidLevels <= 10;
}

std::string MatchingParameters::toString() const {
    std::ostringstream oss;
    oss << "Matching[thresh:" << threshold 
        << ", max:" << maxMatches
        << ", angle:" << angleRange << "°"
        << ", scale:" << scaleMin << "-" << scaleMax
        << ", pyramid:" << pyramidLevels << "]";
    return oss.str();
}

// CalibrationParameters 实现
void CalibrationParameters::addPointPair(const Point2D& pixel, const Coordinate2D& world) {
    pixelPoints.push_back(pixel);
    worldPoints.push_back(world);
}

void CalibrationParameters::clear() {
    pixelPoints.clear();
    worldPoints.clear();
    accuracy = 0.0;
    isValid = false;
}

// DeviceInfo 实现
std::string DeviceInfo::toString() const {
    std::ostringstream oss;
    oss << deviceName << " [" << manufacturer << " " << model << "]"
        << " (ID:" << deviceId << ", SN:" << serialNumber << ")"
        << " " << (isConnected ? "Connected" : "Disconnected");
    return oss.str();
}

// 状态字符串函数实现
const char* GetSystemStatusString(SystemStatus status) {
    switch (status) {
        case SystemStatus::Uninitialized: return "Uninitialized";
        case SystemStatus::Initializing: return "Initializing";
        case SystemStatus::Ready: return "Ready";
        case SystemStatus::Running: return "Running";
        case SystemStatus::Error: return "Error";
        case SystemStatus::Stopped: return "Stopped";
        default: return "Unknown";
    }
}

const char* GetCameraStatusString(CameraStatus status) {
    switch (status) {
        case CameraStatus::Disconnected: return "Disconnected";
        case CameraStatus::Connecting: return "Connecting";
        case CameraStatus::Connected: return "Connected";
        case CameraStatus::Capturing: return "Capturing";
        case CameraStatus::Error: return "Error";
        default: return "Unknown";
    }
}

const char* GetCalibrationStatusString(CalibrationStatus status) {
    switch (status) {
        case CalibrationStatus::NotCalibrated: return "Not Calibrated";
        case CalibrationStatus::Calibrating: return "Calibrating";
        case CalibrationStatus::Calibrated: return "Calibrated";
        case CalibrationStatus::CalibrationError: return "Calibration Error";
        default: return "Unknown";
    }
}

} // namespace VisionModule
