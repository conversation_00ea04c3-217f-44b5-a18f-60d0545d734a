cmake_minimum_required(VERSION 3.16)

project(vision_widgets VERSION 1.0.0 LANGUAGES CXX)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Qt相关设置
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)

# 强制使用Qt5.15
set(QT_VERSION_MAJOR 5)
find_package(Qt5 5.15 REQUIRED COMPONENTS
    Core
    Widgets
    Gui
    LinguistTools
)

# 查找OpenCV
find_package(OpenCV 4.5 REQUIRED)

# 查找Halcon
set(HALCON_ROOT "G:/MVTec/HALCON-23.11-Progress" CACHE PATH "Halcon installation directory")
find_path(HALCON_INCLUDE_DIR
    NAMES HalconCpp.h
    PATHS ${HALCON_ROOT}/include
    NO_DEFAULT_PATH
)

find_library(HALCON_LIBRARY
    NAMES halcon
    PATHS ${H<PERSON>CON_ROOT}/lib/x64-win64
    NO_DEFAULT_PATH
)

find_library(HAL<PERSON>NCPP_LIBRARY
    NAMES halconcpp
    PATHS ${HALCON_ROOT}/lib/x64-win64
    NO_DEFAULT_PATH
)

if(NOT HALCON_INCLUDE_DIR OR NOT HALCON_LIBRARY OR NOT HALCONCPP_LIBRARY)
    message(FATAL_ERROR "Halcon not found. Please check HALCON_ROOT path: ${HALCON_ROOT}")
endif()

message(STATUS "Found Halcon: ${HALCON_ROOT}")
message(STATUS "Halcon include: ${HALCON_INCLUDE_DIR}")
message(STATUS "Halcon libraries: ${HALCON_LIBRARY}, ${HALCONCPP_LIBRARY}")
message(STATUS "Found OpenCV: ${OpenCV_VERSION}")
message(STATUS "Qt version: ${Qt5_VERSION}")

# 翻译文件
set(TS_FILES vision_widgets_zh_CN.ts)

# 源文件组织
set(CORE_SOURCES
    # 核心数据结构
    src/core/VisionTypes.h
    src/core/VisionTypes.cpp
    src/core/ErrorCodes.h

    # 相机控制模块
    src/camera/ICameraInterface.h
    src/camera/CameraController.h
    src/camera/CameraController.cpp
    src/camera/HikvisionCamera.h
    src/camera/HikvisionCamera.cpp

    # 标定管理模块
    src/calibration/CalibrationManager.h
    src/calibration/CalibrationManager.cpp

    # 模板匹配模块
    src/template/TemplateManager.h
    src/template/TemplateManager.cpp

    # 主视觉处理器
    src/vision/VisionProcessor.h
    src/vision/VisionProcessor.cpp
)

set(UI_SOURCES
    # 主界面
    src/ui/MainWindow.h
    src/ui/MainWindow.cpp
    src/ui/MainWindow.ui

    # 自定义控件
    src/ui/widgets/ImageDisplayWidget.h
    src/ui/widgets/ImageDisplayWidget.cpp
    src/ui/widgets/CameraControlWidget.h
    src/ui/widgets/CameraControlWidget.cpp
    src/ui/widgets/CalibrationWidget.h
    src/ui/widgets/CalibrationWidget.cpp
    src/ui/widgets/TemplateWidget.h
    src/ui/widgets/TemplateWidget.cpp

    # ViewModel层
    src/viewmodel/MainViewModel.h
    src/viewmodel/MainViewModel.cpp
    src/viewmodel/CameraViewModel.h
    src/viewmodel/CameraViewModel.cpp
    src/viewmodel/CalibrationViewModel.h
    src/viewmodel/CalibrationViewModel.cpp
    src/viewmodel/TemplateViewModel.h
    src/viewmodel/TemplateViewModel.cpp
)

set(PROJECT_SOURCES
    main.cpp
    widget.cpp
    widget.h
    widget.ui
    ${CORE_SOURCES}
    ${UI_SOURCES}
    ${TS_FILES}
)

# 创建可执行文件 (Qt5)
add_executable(vision_widgets ${PROJECT_SOURCES})

# 创建翻译文件
qt5_create_translation(QM_FILES ${CMAKE_SOURCE_DIR} ${TS_FILES})

# 包含目录
target_include_directories(vision_widgets PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}/src
    ${HALCON_INCLUDE_DIR}
    ${OpenCV_INCLUDE_DIRS}
)

# 链接库
target_link_libraries(vision_widgets PRIVATE
    Qt5::Core
    Qt5::Widgets
    Qt5::Gui
    ${OpenCV_LIBS}
    ${HALCON_LIBRARY}
    ${HALCONCPP_LIBRARY}
)

# 编译器特定设置
if(MSVC)
    target_compile_options(vision_widgets PRIVATE /W4)
    target_compile_definitions(vision_widgets PRIVATE
        _CRT_SECURE_NO_WARNINGS
        NOMINMAX
        WIN32_LEAN_AND_MEAN
    )
else()
    target_compile_options(vision_widgets PRIVATE -Wall -Wextra -Wpedantic)
endif()

# 预处理器定义
target_compile_definitions(vision_widgets PRIVATE
    QT_DEPRECATED_WARNINGS
    QT_DISABLE_DEPRECATED_BEFORE=0x060000
    VISION_MODULE_VERSION="${PROJECT_VERSION}"
)

# 目标属性设置
set_target_properties(vision_widgets PROPERTIES
    WIN32_EXECUTABLE TRUE
    OUTPUT_NAME "VisionModule"
    VERSION ${PROJECT_VERSION}
    SOVERSION ${PROJECT_VERSION_MAJOR}
)

# 安装配置
include(GNUInstallDirs)
install(TARGETS vision_widgets
    RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
    LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
    ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR}
)

# 安装Halcon运行时库 (Windows)
if(WIN32)
    install(FILES
        "${HALCON_ROOT}/bin/x64-win64/halcon.dll"
        "${HALCON_ROOT}/bin/x64-win64/halconcpp.dll"
        DESTINATION ${CMAKE_INSTALL_BINDIR}
        OPTIONAL
    )
endif()

# 调试信息
message(STATUS "Project: ${PROJECT_NAME} v${PROJECT_VERSION}")
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "Install prefix: ${CMAKE_INSTALL_PREFIX}")

# 可选：启用测试
option(BUILD_TESTING "Build tests" OFF)
if(BUILD_TESTING)
    enable_testing()
    add_subdirectory(tests)
endif()
