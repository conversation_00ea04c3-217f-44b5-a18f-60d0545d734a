#ifndef IVISIONCALLBACK_H
#define IVISIONCALLBACK_H

/**
 * @file IVisionCallback.h
 * @brief 视觉模块回调接口定义
 * <AUTHOR> Module Team
 * @version 1.0.0
 * @date 2025-07-04
 */

#include <string>
#include <vector>
#include <memory>
#include "VisionTypes.h"
#include "ErrorCodes.h"

// 前向声明
namespace cv
{
    class Mat;
}

namespace VisionModule
{

    /**
     * @brief 视觉模块事件回调接口
     *
     * 该接口定义了视觉模块各种事件的回调方法，用户可以继承此接口
     * 来接收视觉模块的状态变化、处理结果等信息。
     */
    class IVisionCallback
    {
    public:
        virtual ~IVisionCallback() = default;

        /**
         * @brief 图像采集完成回调
         * @param image 采集到的图像
         * @param timestamp 时间戳（毫秒）
         */
        virtual void OnImageCaptured(const cv::Mat &image, uint64_t timestamp) {}

        /**
         * @brief 连续采集图像回调
         * @param image 采集到的图像
         * @param frameIndex 帧索引
         * @param timestamp 时间戳（毫秒）
         */
        virtual void OnImageStream(const cv::Mat &image, int frameIndex, uint64_t timestamp) {}

        /**
         * @brief 模板匹配结果回调
         * @param results 匹配结果列表
         * @param processingTime 处理时间（毫秒）
         */
        virtual void OnMatchFound(const std::vector<MatchResult> &results, double processingTime) {}

        /**
         * @brief 坐标转换结果回调
         * @param pixelCoord 像素坐标
         * @param physicalCoord 物理坐标
         */
        virtual void OnCoordinateTransformed(const Point2D &pixelCoord, const Coordinate2D &physicalCoord) {}

        /**
         * @brief 标定进度回调
         * @param currentPoints 当前标定点数量
         * @param requiredPoints 需要的标定点数量
         * @param accuracy 当前精度（如果已计算）
         */
        virtual void OnCalibrationProgress(int currentPoints, int requiredPoints, double accuracy) {}

        /**
         * @brief 标定完成回调
         * @param success 标定是否成功
         * @param accuracy 标定精度（mm）
         * @param parameters 标定参数
         */
        virtual void OnCalibrationCompleted(bool success, double accuracy, const CalibrationParameters &parameters) {}

        /**
         * @brief 模板创建进度回调
         * @param progress 进度百分比（0-100）
         * @param message 进度消息
         */
        virtual void OnTemplateCreationProgress(int progress, const std::string &message) {}

        /**
         * @brief 模板创建完成回调
         * @param success 创建是否成功
         * @param templateName 模板名称
         * @param quality 模板质量评分（0-1）
         */
        virtual void OnTemplateCreated(bool success, const std::string &templateName, double quality) {}

        /**
         * @brief 相机状态变化回调
         * @param status 新的相机状态
         * @param deviceInfo 设备信息
         */
        virtual void OnCameraStatusChanged(CameraStatus status, const DeviceInfo &deviceInfo) {}

        /**
         * @brief 相机参数变化回调
         * @param parameters 新的相机参数
         */
        virtual void OnCameraParametersChanged(const CameraParameters &parameters) {}

        /**
         * @brief 系统状态变化回调
         * @param status 新的系统状态
         * @param message 状态消息
         */
        virtual void OnSystemStatusChanged(SystemStatus status, const std::string &message) {}

        /**
         * @brief 错误发生回调
         * @param error 错误码
         * @param message 错误消息
         * @param context 错误上下文（可选）
         */
        virtual void OnError(ErrorCode error, const std::string &message, const std::string &context = "") {}

        /**
         * @brief 警告信息回调
         * @param message 警告消息
         * @param context 警告上下文（可选）
         */
        virtual void OnWarning(const std::string &message, const std::string &context = "") {}

        /**
         * @brief 信息日志回调
         * @param message 信息消息
         * @param level 日志级别（DEBUG, INFO, WARN, ERROR）
         */
        virtual void OnLogMessage(const std::string &message, const std::string &level = "INFO") {}

        /**
         * @brief 性能统计回调
         * @param operation 操作名称
         * @param duration 执行时间（毫秒）
         * @param success 是否成功
         */
        virtual void OnPerformanceStats(const std::string &operation, double duration, bool success) {}

        /**
         * @brief 文件操作完成回调
         * @param operation 操作类型（SAVE, LOAD, EXPORT等）
         * @param filePath 文件路径
         * @param success 是否成功
         */
        virtual void OnFileOperation(const std::string &operation, const std::string &filePath, bool success) {}

        /**
         * @brief 设备连接状态变化回调
         * @param deviceType 设备类型（CAMERA, LIGHT等）
         * @param deviceId 设备ID
         * @param connected 是否已连接
         */
        virtual void OnDeviceConnectionChanged(const std::string &deviceType, const std::string &deviceId, bool connected) {}

        /**
         * @brief 算法执行进度回调
         * @param algorithmName 算法名称
         * @param progress 进度百分比（0-100）
         * @param estimatedTimeRemaining 预计剩余时间（秒）
         */
        virtual void OnAlgorithmProgress(const std::string &algorithmName, int progress, double estimatedTimeRemaining) {}

        /**
         * @brief 内存使用情况回调
         * @param usedMemoryMB 已使用内存（MB）
         * @param totalMemoryMB 总内存（MB）
         * @param warningThreshold 警告阈值
         */
        virtual void OnMemoryUsage(double usedMemoryMB, double totalMemoryMB, bool warningThreshold) {}
    };

    /**
     * @brief 简化的回调接口，只包含最常用的回调方法
     */
    class ISimpleVisionCallback
    {
    public:
        virtual ~ISimpleVisionCallback() = default;

        /**
         * @brief 图像采集回调
         */
        virtual void OnImageCaptured(const cv::Mat &image) {}

        /**
         * @brief 匹配结果回调
         */
        virtual void OnMatchFound(const std::vector<Coordinate2D> &matches) {}

        /**
         * @brief 错误回调
         */
        virtual void OnError(ErrorCode error, const std::string &message) {}

        /**
         * @brief 状态变化回调
         */
        virtual void OnStatusChanged(const std::string &status) {}
    };

    /**
     * @brief 回调管理器，支持多个回调接口注册
     */
    class CallbackManager
    {
    public:
        /**
         * @brief 注册回调接口
         */
        void RegisterCallback(std::shared_ptr<IVisionCallback> callback);

        /**
         * @brief 注册简化回调接口
         */
        void RegisterSimpleCallback(std::shared_ptr<ISimpleVisionCallback> callback);

        /**
         * @brief 注销回调接口
         */
        void UnregisterCallback(std::shared_ptr<IVisionCallback> callback);

        /**
         * @brief 注销简化回调接口
         */
        void UnregisterSimpleCallback(std::shared_ptr<ISimpleVisionCallback> callback);

        /**
         * @brief 清除所有回调
         */
        void ClearAllCallbacks();

        /**
         * @brief 触发图像采集回调
         */
        void TriggerImageCaptured(const cv::Mat &image, uint64_t timestamp = 0);

        /**
         * @brief 触发匹配结果回调
         */
        void TriggerMatchFound(const std::vector<MatchResult> &results, double processingTime = 0);

        /**
         * @brief 触发错误回调
         */
        void TriggerError(ErrorCode error, const std::string &message, const std::string &context = "");

        /**
         * @brief 触发状态变化回调
         */
        void TriggerStatusChanged(SystemStatus status, const std::string &message);

        /**
         * @brief 触发相机状态变化回调
         */
        void TriggerCameraStatusChanged(CameraStatus status, const DeviceInfo &deviceInfo);

        /**
         * @brief 触发相机参数变化回调
         */
        void TriggerCameraParametersChanged(const CameraParameters &parameters);

        /**
         * @brief 触发文件操作回调
         */
        void TriggerFileOperation(const std::string &operation, const std::string &filePath, bool success);

    private:
        std::vector<std::weak_ptr<IVisionCallback>> m_callbacks;
        std::vector<std::weak_ptr<ISimpleVisionCallback>> m_simpleCallbacks;
    };

} // namespace VisionModule

#endif // IVISIONCALLBACK_H
