#include "HikvisionCamera.h"
#include <opencv2/opencv.hpp>
#include <chrono>
#include <iostream>
#include <fstream>
#include <map>
#include <algorithm>

// 注意：这里应该包含海康威视MVS SDK的头文件
// #include "MvCameraControl.h"
// 由于当前环境可能没有安装SDK，我们使用模拟实现

namespace VisionModule
{

    // 静态成员初始化
    std::atomic<bool> HikvisionCamera::s_sdkInitialized{false};
    std::mutex HikvisionCamera::s_sdkMutex;
    int HikvisionCamera::s_instanceCount = 0;

    // 内部实现类
    class HikvisionCamera::Impl
    {
    public:
        void *cameraHandle = nullptr;
        std::string deviceId;
        DeviceInfo deviceInfo;
        CameraParameters currentParams;

        // 模拟相机参数范围
        struct ParameterRange
        {
            double minValue;
            double maxValue;
            double defaultValue;
        };

        std::map<std::string, ParameterRange> parameterRanges = {
            {"Exposure", {0.1, 1000.0, 10.0}},
            {"Gain", {1.0, 50.0, 1.0}},
            {"FrameRate", {1.0, 120.0, 30.0}}};

        Impl()
        {
            // 初始化默认参数
            currentParams.exposure = 10.0;
            currentParams.gain = 1.0;
            currentParams.width = 1920;
            currentParams.height = 1080;
            currentParams.frameRate = 30.0;
            currentParams.format = "RGB8";
            currentParams.autoExposure = false;
            currentParams.autoGain = false;
        }
    };

    HikvisionCamera::HikvisionCamera()
        : m_pImpl(std::make_unique<Impl>()), m_status(CameraStatus::Disconnected), m_isConnected(false), m_isCapturing(false), m_stopCapture(false)
    {

        std::lock_guard<std::mutex> lock(s_sdkMutex);
        s_instanceCount++;

        // 如果是第一个实例，初始化SDK
        if (s_instanceCount == 1 && !s_sdkInitialized)
        {
            InitializeSDK();
        }
    }

    HikvisionCamera::~HikvisionCamera()
    {
        // 确保断开连接
        if (IsConnected())
        {
            Disconnect();
        }

        std::lock_guard<std::mutex> lock(s_sdkMutex);
        s_instanceCount--;

        // 如果是最后一个实例，清理SDK
        if (s_instanceCount == 0 && s_sdkInitialized)
        {
            FinalizeSDK();
        }
    }

    ErrorCode HikvisionCamera::Connect(const std::string &deviceId)
    {
        if (IsConnected())
        {
            return ErrorCode::CAMERA_BUSY;
        }

        SetStatus(CameraStatus::Connecting);

        try
        {
            // 模拟连接过程
            std::this_thread::sleep_for(std::chrono::milliseconds(100));

            // 在实际实现中，这里应该调用海康威视SDK的连接函数
            // 例如：MV_CC_CreateHandle(&m_pImpl->cameraHandle, deviceInfo);
            //      MV_CC_OpenDevice(m_pImpl->cameraHandle);

            m_pImpl->deviceId = deviceId;
            m_pImpl->deviceInfo.deviceId = deviceId;
            m_pImpl->deviceInfo.deviceName = "Hikvision Camera";
            m_pImpl->deviceInfo.manufacturer = "Hikvision";
            m_pImpl->deviceInfo.model = "MV-CA050-10GM";
            m_pImpl->deviceInfo.serialNumber = "HV" + deviceId;
            m_pImpl->deviceInfo.version = "1.0.0";
            m_pImpl->deviceInfo.isConnected = true;

            m_isConnected = true;
            SetStatus(CameraStatus::Connected);

            return ErrorCode::SUCCESS;
        }
        catch (const std::exception &e)
        {
            SetError(ErrorCode::CAMERA_CONNECTION_FAILED, e.what());
            SetStatus(CameraStatus::Error);
            return ErrorCode::CAMERA_CONNECTION_FAILED;
        }
    }

    ErrorCode HikvisionCamera::Disconnect()
    {
        if (!IsConnected())
        {
            return ErrorCode::SUCCESS;
        }

        // 停止连续采集
        if (IsCapturing())
        {
            StopContinuousCapture();
        }

        try
        {
            // 在实际实现中，这里应该调用海康威视SDK的断开函数
            // 例如：MV_CC_CloseDevice(m_pImpl->cameraHandle);
            //      MV_CC_DestroyHandle(m_pImpl->cameraHandle);

            m_pImpl->cameraHandle = nullptr;
            m_pImpl->deviceInfo.isConnected = false;
            m_isConnected = false;
            SetStatus(CameraStatus::Disconnected);

            return ErrorCode::SUCCESS;
        }
        catch (const std::exception &e)
        {
            SetError(ErrorCode::CAMERA_DISCONNECTED, e.what());
            return ErrorCode::CAMERA_DISCONNECTED;
        }
    }

    bool HikvisionCamera::IsConnected() const
    {
        return m_isConnected.load();
    }

    ErrorCode HikvisionCamera::GetDeviceInfo(DeviceInfo &info) const
    {
        if (!IsConnected())
        {
            return ErrorCode::CAMERA_NOT_FOUND;
        }

        info = m_pImpl->deviceInfo;
        return ErrorCode::SUCCESS;
    }

    ErrorCode HikvisionCamera::CaptureImage(cv::Mat &image)
    {
        if (!IsConnected())
        {
            return ErrorCode::CAMERA_NOT_FOUND;
        }

        try
        {
            // 在实际实现中，这里应该调用海康威视SDK的图像采集函数
            // 例如：MV_CC_GetOneFrameTimeout(m_pImpl->cameraHandle, &frameInfo, 5000);

            // 模拟图像采集
            int width = m_pImpl->currentParams.width;
            int height = m_pImpl->currentParams.height;

            // 创建模拟图像（渐变图案）
            image = cv::Mat::zeros(height, width, CV_8UC3);
            for (int y = 0; y < height; y++)
            {
                for (int x = 0; x < width; x++)
                {
                    image.at<cv::Vec3b>(y, x) = cv::Vec3b(
                        static_cast<uchar>((x * 255) / width),
                        static_cast<uchar>((y * 255) / height),
                        128);
                }
            }

            // 添加时间戳文本
            auto now = std::chrono::system_clock::now();
            auto timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
                                 now.time_since_epoch())
                                 .count();

            std::string timeText = "Time: " + std::to_string(timestamp);
            cv::putText(image, timeText, cv::Point(10, 30),
                        cv::FONT_HERSHEY_SIMPLEX, 1, cv::Scalar(255, 255, 255), 2);

            return ErrorCode::SUCCESS;
        }
        catch (const std::exception &e)
        {
            SetError(ErrorCode::CAMERA_CAPTURE_FAILED, e.what());
            return ErrorCode::CAMERA_CAPTURE_FAILED;
        }
    }

    ErrorCode HikvisionCamera::StartContinuousCapture(std::function<void(const cv::Mat &, uint64_t)> callback)
    {
        if (!IsConnected())
        {
            return ErrorCode::CAMERA_NOT_FOUND;
        }

        if (IsCapturing())
        {
            return ErrorCode::CAMERA_BUSY;
        }

        m_imageCallback = callback;
        m_stopCapture = false;
        m_isCapturing = true;

        // 启动采集线程
        m_captureThread = std::thread(&HikvisionCamera::CaptureThreadFunction, this);

        SetStatus(CameraStatus::Capturing);
        return ErrorCode::SUCCESS;
    }

    ErrorCode HikvisionCamera::StopContinuousCapture()
    {
        if (!IsCapturing())
        {
            return ErrorCode::SUCCESS;
        }

        m_stopCapture = true;

        // 等待线程结束
        if (m_captureThread.joinable())
        {
            m_captureThread.join();
        }

        m_isCapturing = false;
        SetStatus(CameraStatus::Connected);

        return ErrorCode::SUCCESS;
    }

    bool HikvisionCamera::IsCapturing() const
    {
        return m_isCapturing.load();
    }

    ErrorCode HikvisionCamera::SetExposure(double exposure)
    {
        if (!IsConnected())
        {
            return ErrorCode::CAMERA_NOT_FOUND;
        }

        if (!ValidateParameter("Exposure", exposure))
        {
            return ErrorCode::CAMERA_PARAMETER_INVALID;
        }

        try
        {
            // 在实际实现中，这里应该调用海康威视SDK的参数设置函数
            // 例如：MV_CC_SetFloatValue(m_pImpl->cameraHandle, "ExposureTime", exposure * 1000);

            m_pImpl->currentParams.exposure = exposure;
            return ErrorCode::SUCCESS;
        }
        catch (const std::exception &e)
        {
            SetError(ErrorCode::CAMERA_PARAMETER_INVALID, e.what());
            return ErrorCode::CAMERA_PARAMETER_INVALID;
        }
    }

    ErrorCode HikvisionCamera::GetExposure(double &exposure) const
    {
        if (!IsConnected())
        {
            return ErrorCode::CAMERA_NOT_FOUND;
        }

        exposure = m_pImpl->currentParams.exposure;
        return ErrorCode::SUCCESS;
    }

    ErrorCode HikvisionCamera::SetGain(double gain)
    {
        if (!IsConnected())
        {
            return ErrorCode::CAMERA_NOT_FOUND;
        }

        if (!ValidateParameter("Gain", gain))
        {
            return ErrorCode::CAMERA_PARAMETER_INVALID;
        }

        try
        {
            // 在实际实现中，这里应该调用海康威视SDK的参数设置函数
            // 例如：MV_CC_SetFloatValue(m_pImpl->cameraHandle, "Gain", gain);

            m_pImpl->currentParams.gain = gain;
            return ErrorCode::SUCCESS;
        }
        catch (const std::exception &e)
        {
            SetError(ErrorCode::CAMERA_PARAMETER_INVALID, e.what());
            return ErrorCode::CAMERA_PARAMETER_INVALID;
        }
    }

    ErrorCode HikvisionCamera::GetGain(double &gain) const
    {
        if (!IsConnected())
        {
            return ErrorCode::CAMERA_NOT_FOUND;
        }

        gain = m_pImpl->currentParams.gain;
        return ErrorCode::SUCCESS;
    }

    ErrorCode HikvisionCamera::SetImageSize(int width, int height)
    {
        if (!IsConnected())
        {
            return ErrorCode::CAMERA_NOT_FOUND;
        }

        if (width <= 0 || height <= 0 || width > 4096 || height > 3000)
        {
            return ErrorCode::CAMERA_PARAMETER_INVALID;
        }

        try
        {
            // 在实际实现中，这里应该调用海康威视SDK的参数设置函数
            // 例如：MV_CC_SetIntValue(m_pImpl->cameraHandle, "Width", width);
            //      MV_CC_SetIntValue(m_pImpl->cameraHandle, "Height", height);

            m_pImpl->currentParams.width = width;
            m_pImpl->currentParams.height = height;
            return ErrorCode::SUCCESS;
        }
        catch (const std::exception &e)
        {
            SetError(ErrorCode::CAMERA_PARAMETER_INVALID, e.what());
            return ErrorCode::CAMERA_PARAMETER_INVALID;
        }
    }

    ErrorCode HikvisionCamera::GetImageSize(int &width, int &height) const
    {
        if (!IsConnected())
        {
            return ErrorCode::CAMERA_NOT_FOUND;
        }

        width = m_pImpl->currentParams.width;
        height = m_pImpl->currentParams.height;
        return ErrorCode::SUCCESS;
    }

    ErrorCode HikvisionCamera::SetFrameRate(double frameRate)
    {
        if (!IsConnected())
        {
            return ErrorCode::CAMERA_NOT_FOUND;
        }

        if (!ValidateParameter("FrameRate", frameRate))
        {
            return ErrorCode::CAMERA_PARAMETER_INVALID;
        }

        try
        {
            // 在实际实现中，这里应该调用海康威视SDK的参数设置函数
            // 例如：MV_CC_SetFloatValue(m_pImpl->cameraHandle, "AcquisitionFrameRate", frameRate);

            m_pImpl->currentParams.frameRate = frameRate;
            return ErrorCode::SUCCESS;
        }
        catch (const std::exception &e)
        {
            SetError(ErrorCode::CAMERA_PARAMETER_INVALID, e.what());
            return ErrorCode::CAMERA_PARAMETER_INVALID;
        }
    }

    ErrorCode HikvisionCamera::GetFrameRate(double &frameRate) const
    {
        if (!IsConnected())
        {
            return ErrorCode::CAMERA_NOT_FOUND;
        }

        frameRate = m_pImpl->currentParams.frameRate;
        return ErrorCode::SUCCESS;
    }

    ErrorCode HikvisionCamera::SetTriggerMode(const std::string &triggerMode)
    {
        if (!IsConnected())
        {
            return ErrorCode::CAMERA_NOT_FOUND;
        }

        if (triggerMode != "Software" && triggerMode != "Hardware" && triggerMode != "Continuous")
        {
            return ErrorCode::CAMERA_PARAMETER_INVALID;
        }

        try
        {
            // 在实际实现中，这里应该调用海康威视SDK的参数设置函数
            // 例如：MV_CC_SetEnumValue(m_pImpl->cameraHandle, "TriggerMode", triggerMode);

            m_pImpl->currentParams.triggerMode = triggerMode;
            return ErrorCode::SUCCESS;
        }
        catch (const std::exception &e)
        {
            SetError(ErrorCode::CAMERA_PARAMETER_INVALID, e.what());
            return ErrorCode::CAMERA_PARAMETER_INVALID;
        }
    }

    ErrorCode HikvisionCamera::GetTriggerMode(std::string &triggerMode) const
    {
        if (!IsConnected())
        {
            return ErrorCode::CAMERA_NOT_FOUND;
        }

        triggerMode = m_pImpl->currentParams.triggerMode;
        return ErrorCode::SUCCESS;
    }

    ErrorCode HikvisionCamera::SoftwareTrigger()
    {
        if (!IsConnected())
        {
            return ErrorCode::CAMERA_NOT_FOUND;
        }

        if (m_pImpl->currentParams.triggerMode != "Software")
        {
            return ErrorCode::CAMERA_PARAMETER_INVALID;
        }

        try
        {
            // 在实际实现中，这里应该调用海康威视SDK的软件触发函数
            // 例如：MV_CC_SetCommandValue(m_pImpl->cameraHandle, "TriggerSoftware");

            return ErrorCode::SUCCESS;
        }
        catch (const std::exception &e)
        {
            SetError(ErrorCode::CAMERA_SDK_ERROR, e.what());
            return ErrorCode::CAMERA_SDK_ERROR;
        }
    }

    ErrorCode HikvisionCamera::SetParameters(const CameraParameters &parameters)
    {
        if (!IsConnected())
        {
            return ErrorCode::CAMERA_NOT_FOUND;
        }

        if (!parameters.isValid())
        {
            return ErrorCode::CAMERA_PARAMETER_INVALID;
        }

        try
        {
            // 批量设置参数
            ErrorCode result = ErrorCode::SUCCESS;

            if ((result = SetExposure(parameters.exposure)) != ErrorCode::SUCCESS)
                return result;
            if ((result = SetGain(parameters.gain)) != ErrorCode::SUCCESS)
                return result;
            if ((result = SetImageSize(parameters.width, parameters.height)) != ErrorCode::SUCCESS)
                return result;
            if ((result = SetFrameRate(parameters.frameRate)) != ErrorCode::SUCCESS)
                return result;
            if ((result = SetTriggerMode(parameters.triggerMode)) != ErrorCode::SUCCESS)
                return result;

            // 设置其他参数
            m_pImpl->currentParams.format = parameters.format;
            m_pImpl->currentParams.autoExposure = parameters.autoExposure;
            m_pImpl->currentParams.autoGain = parameters.autoGain;

            return ErrorCode::SUCCESS;
        }
        catch (const std::exception &e)
        {
            SetError(ErrorCode::CAMERA_PARAMETER_INVALID, e.what());
            return ErrorCode::CAMERA_PARAMETER_INVALID;
        }
    }

    ErrorCode HikvisionCamera::GetParameters(CameraParameters &parameters) const
    {
        if (!IsConnected())
        {
            return ErrorCode::CAMERA_NOT_FOUND;
        }

        parameters = m_pImpl->currentParams;
        return ErrorCode::SUCCESS;
    }

    ErrorCode HikvisionCamera::GetParameterRange(const std::string &paramName, double &minValue, double &maxValue) const
    {
        auto it = m_pImpl->parameterRanges.find(paramName);
        if (it == m_pImpl->parameterRanges.end())
        {
            return ErrorCode::CAMERA_PARAMETER_INVALID;
        }

        minValue = it->second.minValue;
        maxValue = it->second.maxValue;
        return ErrorCode::SUCCESS;
    }

    ErrorCode HikvisionCamera::ResetToDefault()
    {
        if (!IsConnected())
        {
            return ErrorCode::CAMERA_NOT_FOUND;
        }

        try
        {
            // 重置为默认参数
            CameraParameters defaultParams;
            defaultParams.exposure = m_pImpl->parameterRanges["Exposure"].defaultValue;
            defaultParams.gain = m_pImpl->parameterRanges["Gain"].defaultValue;
            defaultParams.frameRate = m_pImpl->parameterRanges["FrameRate"].defaultValue;
            defaultParams.width = 1920;
            defaultParams.height = 1080;
            defaultParams.format = "RGB8";
            defaultParams.triggerMode = "Continuous";
            defaultParams.autoExposure = false;
            defaultParams.autoGain = false;

            return SetParameters(defaultParams);
        }
        catch (const std::exception &e)
        {
            SetError(ErrorCode::CAMERA_PARAMETER_INVALID, e.what());
            return ErrorCode::CAMERA_PARAMETER_INVALID;
        }
    }

    ErrorCode HikvisionCamera::SaveConfiguration(const std::string &filePath)
    {
        if (!IsConnected())
        {
            return ErrorCode::CAMERA_NOT_FOUND;
        }

        try
        {
            // 在实际实现中，这里应该调用海康威视SDK的配置保存函数
            // 例如：MV_CC_FeatureSave(m_pImpl->cameraHandle, filePath.c_str());

            // 模拟保存配置文件
            std::ofstream file(filePath);
            if (!file.is_open())
            {
                return ErrorCode::FILE_ACCESS_DENIED;
            }

            file << "# Hikvision Camera Configuration\n";
            file << "Exposure=" << m_pImpl->currentParams.exposure << "\n";
            file << "Gain=" << m_pImpl->currentParams.gain << "\n";
            file << "Width=" << m_pImpl->currentParams.width << "\n";
            file << "Height=" << m_pImpl->currentParams.height << "\n";
            file << "FrameRate=" << m_pImpl->currentParams.frameRate << "\n";
            file << "Format=" << m_pImpl->currentParams.format << "\n";
            file << "TriggerMode=" << m_pImpl->currentParams.triggerMode << "\n";

            file.close();
            return ErrorCode::SUCCESS;
        }
        catch (const std::exception &e)
        {
            SetError(ErrorCode::FILE_ACCESS_DENIED, e.what());
            return ErrorCode::FILE_ACCESS_DENIED;
        }
    }

    ErrorCode HikvisionCamera::LoadConfiguration(const std::string &filePath)
    {
        if (!IsConnected())
        {
            return ErrorCode::CAMERA_NOT_FOUND;
        }

        try
        {
            // 在实际实现中，这里应该调用海康威视SDK的配置加载函数
            // 例如：MV_CC_FeatureLoad(m_pImpl->cameraHandle, filePath.c_str());

            // 模拟加载配置文件
            std::ifstream file(filePath);
            if (!file.is_open())
            {
                return ErrorCode::FILE_NOT_FOUND;
            }

            std::string line;
            CameraParameters params = m_pImpl->currentParams;

            while (std::getline(file, line))
            {
                if (line.empty() || line[0] == '#')
                    continue;

                size_t pos = line.find('=');
                if (pos == std::string::npos)
                    continue;

                std::string key = line.substr(0, pos);
                std::string value = line.substr(pos + 1);

                if (key == "Exposure")
                    params.exposure = std::stod(value);
                else if (key == "Gain")
                    params.gain = std::stod(value);
                else if (key == "Width")
                    params.width = std::stoi(value);
                else if (key == "Height")
                    params.height = std::stoi(value);
                else if (key == "FrameRate")
                    params.frameRate = std::stod(value);
                else if (key == "Format")
                    params.format = value;
                else if (key == "TriggerMode")
                    params.triggerMode = value;
            }

            file.close();
            return SetParameters(params);
        }
        catch (const std::exception &e)
        {
            SetError(ErrorCode::FILE_FORMAT_ERROR, e.what());
            return ErrorCode::FILE_FORMAT_ERROR;
        }
    }

    CameraStatus HikvisionCamera::GetStatus() const
    {
        return m_status.load();
    }

    std::string HikvisionCamera::GetLastError() const
    {
        std::lock_guard<std::mutex> lock(m_errorMutex);
        return m_lastError;
    }

    std::string HikvisionCamera::GetManufacturer() const
    {
        return "Hikvision";
    }

    std::string HikvisionCamera::GetModel() const
    {
        return IsConnected() ? m_pImpl->deviceInfo.model : "Unknown";
    }

    std::string HikvisionCamera::GetSDKVersion() const
    {
        return "MVS SDK 2.1.2";
    }

    bool HikvisionCamera::IsFeatureSupported(const std::string &feature) const
    {
        // 支持的功能列表
        static const std::vector<std::string> supportedFeatures = {
            "Exposure", "Gain", "FrameRate", "TriggerMode",
            "SoftwareTrigger", "ImageSize", "PixelFormat",
            "AutoExposure", "AutoGain"};

        return std::find(supportedFeatures.begin(), supportedFeatures.end(), feature) != supportedFeatures.end();
    }

    void HikvisionCamera::SetErrorCallback(std::function<void(ErrorCode, const std::string &)> callback)
    {
        m_errorCallback = callback;
    }

    void HikvisionCamera::SetStatusCallback(std::function<void(CameraStatus)> callback)
    {
        m_statusCallback = callback;
    }

    // 静态方法实现
    std::string HikvisionCamera::GetSDKErrorDescription(int errorCode)
    {
        // 在实际实现中，这里应该调用海康威视SDK的错误描述函数
        // 例如：return MV_CC_GetErrorString(errorCode);

        // 模拟错误描述
        switch (errorCode)
        {
        case 0:
            return "Success";
        case -1:
            return "Generic error";
        case -2:
            return "Invalid parameter";
        case -3:
            return "Device not found";
        case -4:
            return "Device access denied";
        default:
            return "Unknown error: " + std::to_string(errorCode);
        }
    }

    ErrorCode HikvisionCamera::InitializeSDK()
    {
        std::lock_guard<std::mutex> lock(s_sdkMutex);

        if (s_sdkInitialized)
        {
            return ErrorCode::SUCCESS;
        }

        try
        {
            // 在实际实现中，这里应该调用海康威视SDK的初始化函数
            // 例如：int result = MV_CC_Initialize();

            // 模拟SDK初始化
            s_sdkInitialized = true;
            return ErrorCode::SUCCESS;
        }
        catch (const std::exception &e)
        {
            return ErrorCode::LIBRARY_LOAD_FAILED;
        }
    }

    ErrorCode HikvisionCamera::FinalizeSDK()
    {
        std::lock_guard<std::mutex> lock(s_sdkMutex);

        if (!s_sdkInitialized)
        {
            return ErrorCode::SUCCESS;
        }

        try
        {
            // 在实际实现中，这里应该调用海康威视SDK的清理函数
            // 例如：MV_CC_Finalize();

            // 模拟SDK清理
            s_sdkInitialized = false;
            return ErrorCode::SUCCESS;
        }
        catch (const std::exception &e)
        {
            return ErrorCode::LIBRARY_LOAD_FAILED;
        }
    }

    std::vector<DeviceInfo> HikvisionCamera::ScanDevices()
    {
        std::vector<DeviceInfo> devices;

        if (!s_sdkInitialized)
        {
            return devices;
        }

        try
        {
            // 在实际实现中，这里应该调用海康威视SDK的设备扫描函数
            // 例如：MV_CC_EnumDevices(MV_GIGE_DEVICE | MV_USB_DEVICE, &deviceList);

            // 模拟设备扫描
            for (int i = 0; i < 2; i++)
            {
                DeviceInfo info;
                info.deviceId = "HV_" + std::to_string(i);
                info.deviceName = "Hikvision Camera " + std::to_string(i);
                info.manufacturer = "Hikvision";
                info.model = "MV-CA050-10GM";
                info.serialNumber = "HV123456" + std::to_string(i);
                info.version = "1.0.0";
                info.isConnected = false;
                devices.push_back(info);
            }

            return devices;
        }
        catch (const std::exception &e)
        {
            return devices;
        }
    }

    // 私有方法实现
    void HikvisionCamera::SetStatus(CameraStatus status)
    {
        CameraStatus oldStatus = m_status.exchange(status);
        if (oldStatus != status && m_statusCallback)
        {
            m_statusCallback(status);
        }
    }

    void HikvisionCamera::SetError(ErrorCode error, const std::string &message)
    {
        {
            std::lock_guard<std::mutex> lock(m_errorMutex);
            m_lastError = message;
        }

        if (m_errorCallback)
        {
            m_errorCallback(error, message);
        }
    }

    void HikvisionCamera::CaptureThreadFunction()
    {
        int frameIndex = 0;
        auto frameInterval = std::chrono::milliseconds(static_cast<int>(1000.0 / m_pImpl->currentParams.frameRate));

        while (!m_stopCapture)
        {
            try
            {
                cv::Mat image;
                if (CaptureImage(image) == ErrorCode::SUCCESS && m_imageCallback)
                {
                    auto timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
                                         std::chrono::system_clock::now().time_since_epoch())
                                         .count();
                    m_imageCallback(image, timestamp);
                    frameIndex++;
                }

                std::this_thread::sleep_for(frameInterval);
            }
            catch (const std::exception &e)
            {
                SetError(ErrorCode::CAMERA_CAPTURE_FAILED, e.what());
                break;
            }
        }
    }

    ErrorCode HikvisionCamera::ConvertSDKError(int sdkError) const
    {
        switch (sdkError)
        {
        case 0:
            return ErrorCode::SUCCESS;
        case -1:
            return ErrorCode::CAMERA_SDK_ERROR;
        case -2:
            return ErrorCode::CAMERA_PARAMETER_INVALID;
        case -3:
            return ErrorCode::CAMERA_NOT_FOUND;
        case -4:
            return ErrorCode::CAMERA_PERMISSION_DENIED;
        case -5:
            return ErrorCode::CAMERA_TIMEOUT;
        default:
            return ErrorCode::CAMERA_SDK_ERROR;
        }
    }

    bool HikvisionCamera::ValidateParameter(const std::string &paramName, double value) const
    {
        auto it = m_pImpl->parameterRanges.find(paramName);
        if (it == m_pImpl->parameterRanges.end())
        {
            return false;
        }

        return value >= it->second.minValue && value <= it->second.maxValue;
    }

    void *HikvisionCamera::GetCameraHandle() const
    {
        return m_pImpl->cameraHandle;
    }

    bool HikvisionCamera::IsCameraOpened() const
    {
        return m_pImpl->cameraHandle != nullptr;
    }

    ErrorCode HikvisionCamera::WaitForImage(int timeoutMs)
    {
        // 在实际实现中，这里应该等待图像采集完成
        // 例如：MV_CC_GetOneFrameTimeout(m_pImpl->cameraHandle, &frameInfo, timeoutMs);

        // 模拟等待
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
        return ErrorCode::SUCCESS;
    }

    ErrorCode HikvisionCamera::ConvertImage(const unsigned char *srcData, size_t srcSize,
                                            int width, int height, int pixelFormat, cv::Mat &dstImage)
    {
        try
        {
            // 在实际实现中，这里应该根据像素格式转换图像
            // 例如：MV_CC_ConvertPixelType(m_pImpl->cameraHandle, &convertParam);

            // 模拟图像转换
            if (pixelFormat == 0x01080001) // Mono8
            {
                dstImage = cv::Mat(height, width, CV_8UC1, const_cast<unsigned char *>(srcData)).clone();
            }
            else if (pixelFormat == 0x02180014) // RGB8
            {
                dstImage = cv::Mat(height, width, CV_8UC3, const_cast<unsigned char *>(srcData)).clone();
            }
            else
            {
                // 默认转换为RGB
                dstImage = cv::Mat::zeros(height, width, CV_8UC3);
            }

            return ErrorCode::SUCCESS;
        }
        catch (const std::exception &e)
        {
            SetError(ErrorCode::IMAGE_PROCESSING_FAILED, e.what());
            return ErrorCode::IMAGE_PROCESSING_FAILED;
        }
    }

    std::string HikvisionCamera::GetPixelFormatString(int pixelFormat) const
    {
        switch (pixelFormat)
        {
        case 0x01080001:
            return "Mono8";
        case 0x02180014:
            return "RGB8";
        case 0x0210001C:
            return "BGR8";
        default:
            return "Unknown";
        }
    }

    bool HikvisionCamera::IsSDKInitialized()
    {
        return s_sdkInitialized.load();
    }

    // 海康威视相机工厂类实现
    std::unique_ptr<ICameraInterface> HikvisionCameraFactory::CreateCamera(const std::string &manufacturer)
    {
        if (manufacturer == "Hikvision" || manufacturer == "hikvision" || manufacturer == "HIKVISION")
        {
            return std::make_unique<HikvisionCamera>();
        }
        return nullptr;
    }

    std::vector<std::string> HikvisionCameraFactory::GetSupportedManufacturers() const
    {
        return {"Hikvision"};
    }

    std::vector<DeviceInfo> HikvisionCameraFactory::ScanDevices(const std::string &manufacturer)
    {
        if (manufacturer.empty() || manufacturer == "Hikvision")
        {
            return HikvisionCamera::ScanDevices();
        }
        return {};
    }

} // namespace VisionModule
