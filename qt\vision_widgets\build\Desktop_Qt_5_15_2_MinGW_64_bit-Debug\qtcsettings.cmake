# This file is managed by Qt Creator, do not edit!

set("CMAKE_GENERATOR" "MinG<PERSON> Makefiles" CACHE "STRING" "" FORCE)
set("CMAKE_COLOR_DIAGNOSTICS" "ON" CACHE "BOOL" "" FORCE)
set("QT_QMAKE_EXECUTABLE" "D:/Qt_5.15/5.15.2/mingw81_64/bin/qmake.exe" CACHE "FILEPATH" "" FORCE)
set("CMAKE_BUILD_TYPE" "Debug" CACHE "STRING" "" FORCE)
set("CMAKE_PREFIX_PATH" "D:/Qt_5.15/5.15.2/mingw81_64" CACHE "PATH" "" FORCE)
set("CMAKE_PROJECT_INCLUDE_BEFORE" "F:/Project/QT_Projact/qt/delta/vison_v4/qt/vision_widgets/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/.qtc/package-manager/auto-setup.cmake" CACHE "FILEPATH" "" FORCE)
set("CMAKE_CXX_FLAGS_INIT" "-DQT_QML_DEBUG" CACHE "STRING" "" FORCE)
set("CMAKE_C_COMPILER" "D:/Qt_5.15/Tools/mingw810_64/bin/gcc.exe" CACHE "FILEPATH" "" FORCE)
set("CMAKE_CXX_COMPILER" "D:/Qt_5.15/Tools/mingw810_64/bin/g++.exe" CACHE "FILEPATH" "" FORCE)