#include "CameraController.h"
#include "HikvisionCamera.h"
#include <algorithm>
#include <iostream>
#include <chrono>

namespace VisionModule
{

    // 内部实现类
    class CameraController::Impl
    {
    public:
        std::unique_ptr<ICameraInterface> currentCamera;
        std::string currentManufacturer;
        std::vector<std::unique_ptr<ICameraFactory>> factories;

        Impl()
        {
            // 注册支持的相机工厂
            factories.push_back(std::make_unique<HikvisionCameraFactory>());
            // 可以在这里添加其他厂商的工厂
            // factories.push_back(std::make_unique<BaslerCameraFactory>());
            // factories.push_back(std::make_unique<DahuaCameraFactory>());
        }

        ICameraFactory *GetFactory(const std::string &manufacturer)
        {
            for (auto &factory : factories)
            {
                auto supported = factory->GetSupportedManufacturers();
                if (std::find(supported.begin(), supported.end(), manufacturer) != supported.end())
                {
                    return factory.get();
                }
            }
            return nullptr;
        }

        std::vector<std::string> GetAllSupportedManufacturers()
        {
            std::vector<std::string> allManufacturers;
            for (auto &factory : factories)
            {
                auto supported = factory->GetSupportedManufacturers();
                allManufacturers.insert(allManufacturers.end(), supported.begin(), supported.end());
            }
            return allManufacturers;
        }
    };

    CameraController::CameraController()
        : m_pImpl(std::make_unique<Impl>()), m_initialized(false)
    {
    }

    CameraController::~CameraController()
    {
        if (m_initialized)
        {
            Finalize();
        }
    }

    ErrorCode CameraController::Initialize()
    {
        std::lock_guard<std::mutex> lock(m_mutex);

        if (m_initialized)
        {
            return ErrorCode::SUCCESS;
        }

        try
        {
            // 初始化所有相机SDK
            ErrorCode result = HikvisionCamera::InitializeSDK();
            if (result != ErrorCode::SUCCESS)
            {
                SetError(result, "Failed to initialize Hikvision SDK");
                return result;
            }

            // 可以在这里初始化其他厂商的SDK

            m_initialized = true;
            return ErrorCode::SUCCESS;
        }
        catch (const std::exception &e)
        {
            SetError(ErrorCode::SYSTEM_NOT_INITIALIZED, e.what());
            return ErrorCode::SYSTEM_NOT_INITIALIZED;
        }
    }

    ErrorCode CameraController::Finalize()
    {
        std::lock_guard<std::mutex> lock(m_mutex);

        if (!m_initialized)
        {
            return ErrorCode::SUCCESS;
        }

        try
        {
            // 断开当前相机
            if (IsCameraConnected())
            {
                DisconnectCamera();
            }

            // 清理所有相机SDK
            HikvisionCamera::FinalizeSDK();
            // 可以在这里清理其他厂商的SDK

            m_initialized = false;
            return ErrorCode::SUCCESS;
        }
        catch (const std::exception &e)
        {
            SetError(ErrorCode::SYSTEM_NOT_INITIALIZED, e.what());
            return ErrorCode::SYSTEM_NOT_INITIALIZED;
        }
    }

    bool CameraController::IsInitialized() const
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        return m_initialized;
    }

    std::vector<DeviceInfo> CameraController::ScanDevices(const std::string &manufacturer)
    {
        std::vector<DeviceInfo> allDevices;

        if (!IsInitialized())
        {
            return allDevices;
        }

        try
        {
            if (manufacturer.empty())
            {
                // 扫描所有厂商的设备
                for (auto &factory : m_pImpl->factories)
                {
                    auto devices = factory->ScanDevices();
                    allDevices.insert(allDevices.end(), devices.begin(), devices.end());
                }
            }
            else
            {
                // 扫描指定厂商的设备
                auto factory = m_pImpl->GetFactory(manufacturer);
                if (factory)
                {
                    auto devices = factory->ScanDevices(manufacturer);
                    allDevices.insert(allDevices.end(), devices.begin(), devices.end());
                }
            }

            return allDevices;
        }
        catch (const std::exception &e)
        {
            SetError(ErrorCode::CAMERA_NOT_FOUND, e.what());
            return allDevices;
        }
    }

    ErrorCode CameraController::ConnectCamera(const std::string &deviceId, const std::string &manufacturer)
    {
        if (!IsInitialized())
        {
            return ErrorCode::SYSTEM_NOT_INITIALIZED;
        }

        if (IsCameraConnected())
        {
            return ErrorCode::CAMERA_BUSY;
        }

        try
        {
            std::string targetManufacturer = manufacturer;

            // 如果未指定厂商，尝试自动识别
            if (targetManufacturer.empty())
            {
                targetManufacturer = AutoDetectManufacturer(deviceId);
                if (targetManufacturer.empty())
                {
                    SetError(ErrorCode::CAMERA_NOT_FOUND, "Cannot detect camera manufacturer for device: " + deviceId);
                    return ErrorCode::CAMERA_NOT_FOUND;
                }
            }

            // 获取对应的工厂
            auto factory = m_pImpl->GetFactory(targetManufacturer);
            if (!factory)
            {
                SetError(ErrorCode::CAMERA_NOT_FOUND, "Unsupported manufacturer: " + targetManufacturer);
                return ErrorCode::CAMERA_NOT_FOUND;
            }

            // 创建相机实例
            auto camera = factory->CreateCamera(targetManufacturer);
            if (!camera)
            {
                SetError(ErrorCode::CAMERA_NOT_FOUND, "Failed to create camera instance for: " + targetManufacturer);
                return ErrorCode::CAMERA_NOT_FOUND;
            }

            // 设置回调
            camera->SetErrorCallback([this](ErrorCode error, const std::string &message)
                                     { SetError(error, message); });

            camera->SetStatusCallback([this](CameraStatus status)
                                      {
            if (m_statusCallback) {
                m_statusCallback(status);
            }
            if (m_callbackManager) {
                DeviceInfo info;
                if (m_pImpl->currentCamera && m_pImpl->currentCamera->GetDeviceInfo(info) == ErrorCode::SUCCESS) {
                    m_callbackManager->TriggerCameraStatusChanged(status, info);
                }
            } });

            // 连接相机
            ErrorCode result = camera->Connect(deviceId);
            if (result != ErrorCode::SUCCESS)
            {
                SetError(result, "Failed to connect to camera: " + deviceId);
                return result;
            }

            // 保存相机实例
            m_pImpl->currentCamera = std::move(camera);
            m_pImpl->currentManufacturer = targetManufacturer;

            return ErrorCode::SUCCESS;
        }
        catch (const std::exception &e)
        {
            SetError(ErrorCode::CAMERA_CONNECTION_FAILED, e.what());
            return ErrorCode::CAMERA_CONNECTION_FAILED;
        }
    }

    ErrorCode CameraController::DisconnectCamera()
    {
        if (!IsCameraConnected())
        {
            return ErrorCode::SUCCESS;
        }

        try
        {
            ErrorCode result = m_pImpl->currentCamera->Disconnect();
            m_pImpl->currentCamera.reset();
            m_pImpl->currentManufacturer.clear();

            return result;
        }
        catch (const std::exception &e)
        {
            SetError(ErrorCode::CAMERA_DISCONNECTED, e.what());
            return ErrorCode::CAMERA_DISCONNECTED;
        }
    }

    bool CameraController::IsCameraConnected() const
    {
        return m_pImpl->currentCamera && m_pImpl->currentCamera->IsConnected();
    }

    ErrorCode CameraController::GetCurrentCameraInfo(DeviceInfo &info) const
    {
        if (!IsCameraConnected())
        {
            return ErrorCode::CAMERA_NOT_FOUND;
        }

        return m_pImpl->currentCamera->GetDeviceInfo(info);
    }

    ErrorCode CameraController::CaptureImage(cv::Mat &image)
    {
        ErrorCode result = ValidateCamera();
        if (result != ErrorCode::SUCCESS)
        {
            return result;
        }

        try
        {
            result = m_pImpl->currentCamera->CaptureImage(image);

            // 触发回调
            if (result == ErrorCode::SUCCESS && m_callbackManager)
            {
                auto timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
                                     std::chrono::system_clock::now().time_since_epoch())
                                     .count();
                m_callbackManager->TriggerImageCaptured(image, timestamp);
            }

            return result;
        }
        catch (const std::exception &e)
        {
            SetError(ErrorCode::CAMERA_CAPTURE_FAILED, e.what());
            return ErrorCode::CAMERA_CAPTURE_FAILED;
        }
    }

    ErrorCode CameraController::StartContinuousCapture(std::function<void(const cv::Mat &, uint64_t)> callback)
    {
        ErrorCode result = ValidateCamera();
        if (result != ErrorCode::SUCCESS)
        {
            return result;
        }

        try
        {
            // 包装回调函数，添加回调管理器的触发
            auto wrappedCallback = [this, callback](const cv::Mat &image, uint64_t timestamp)
            {
                if (callback)
                {
                    callback(image, timestamp);
                }
                if (m_callbackManager)
                {
                    m_callbackManager->TriggerImageCaptured(image, timestamp);
                }
            };

            return m_pImpl->currentCamera->StartContinuousCapture(wrappedCallback);
        }
        catch (const std::exception &e)
        {
            SetError(ErrorCode::CAMERA_CAPTURE_FAILED, e.what());
            return ErrorCode::CAMERA_CAPTURE_FAILED;
        }
    }

    ErrorCode CameraController::StopContinuousCapture()
    {
        ErrorCode result = ValidateCamera();
        if (result != ErrorCode::SUCCESS)
        {
            return result;
        }

        return m_pImpl->currentCamera->StopContinuousCapture();
    }

    bool CameraController::IsCapturing() const
    {
        if (!IsCameraConnected())
        {
            return false;
        }

        return m_pImpl->currentCamera->IsCapturing();
    }

    ErrorCode CameraController::SetCameraParameters(const CameraParameters &parameters)
    {
        ErrorCode result = ValidateCamera();
        if (result != ErrorCode::SUCCESS)
        {
            return result;
        }

        try
        {
            result = m_pImpl->currentCamera->SetParameters(parameters);

            // 触发参数变化回调
            if (result == ErrorCode::SUCCESS && m_callbackManager)
            {
                m_callbackManager->TriggerCameraParametersChanged(parameters);
            }

            return result;
        }
        catch (const std::exception &e)
        {
            SetError(ErrorCode::CAMERA_PARAMETER_INVALID, e.what());
            return ErrorCode::CAMERA_PARAMETER_INVALID;
        }
    }

    ErrorCode CameraController::GetCameraParameters(CameraParameters &parameters) const
    {
        ErrorCode result = ValidateCamera();
        if (result != ErrorCode::SUCCESS)
        {
            return result;
        }

        return m_pImpl->currentCamera->GetParameters(parameters);
    }

    ErrorCode CameraController::SetExposure(double exposure)
    {
        ErrorCode result = ValidateCamera();
        if (result != ErrorCode::SUCCESS)
        {
            return result;
        }

        return m_pImpl->currentCamera->SetExposure(exposure);
    }

    ErrorCode CameraController::GetExposure(double &exposure) const
    {
        ErrorCode result = ValidateCamera();
        if (result != ErrorCode::SUCCESS)
        {
            return result;
        }

        return m_pImpl->currentCamera->GetExposure(exposure);
    }

    ErrorCode CameraController::SetGain(double gain)
    {
        ErrorCode result = ValidateCamera();
        if (result != ErrorCode::SUCCESS)
        {
            return result;
        }

        return m_pImpl->currentCamera->SetGain(gain);
    }

    ErrorCode CameraController::GetGain(double &gain) const
    {
        ErrorCode result = ValidateCamera();
        if (result != ErrorCode::SUCCESS)
        {
            return result;
        }

        return m_pImpl->currentCamera->GetGain(gain);
    }

    ErrorCode CameraController::SetImageSize(int width, int height)
    {
        ErrorCode result = ValidateCamera();
        if (result != ErrorCode::SUCCESS)
        {
            return result;
        }

        return m_pImpl->currentCamera->SetImageSize(width, height);
    }

    ErrorCode CameraController::GetImageSize(int &width, int &height) const
    {
        ErrorCode result = ValidateCamera();
        if (result != ErrorCode::SUCCESS)
        {
            return result;
        }

        return m_pImpl->currentCamera->GetImageSize(width, height);
    }

    ErrorCode CameraController::SetFrameRate(double frameRate)
    {
        ErrorCode result = ValidateCamera();
        if (result != ErrorCode::SUCCESS)
        {
            return result;
        }

        return m_pImpl->currentCamera->SetFrameRate(frameRate);
    }

    ErrorCode CameraController::GetFrameRate(double &frameRate) const
    {
        ErrorCode result = ValidateCamera();
        if (result != ErrorCode::SUCCESS)
        {
            return result;
        }

        return m_pImpl->currentCamera->GetFrameRate(frameRate);
    }

    ErrorCode CameraController::SetTriggerMode(const std::string &triggerMode)
    {
        ErrorCode result = ValidateCamera();
        if (result != ErrorCode::SUCCESS)
        {
            return result;
        }

        return m_pImpl->currentCamera->SetTriggerMode(triggerMode);
    }

    ErrorCode CameraController::GetTriggerMode(std::string &triggerMode) const
    {
        ErrorCode result = ValidateCamera();
        if (result != ErrorCode::SUCCESS)
        {
            return result;
        }

        return m_pImpl->currentCamera->GetTriggerMode(triggerMode);
    }

    ErrorCode CameraController::SoftwareTrigger()
    {
        ErrorCode result = ValidateCamera();
        if (result != ErrorCode::SUCCESS)
        {
            return result;
        }

        return m_pImpl->currentCamera->SoftwareTrigger();
    }

    ErrorCode CameraController::GetParameterRange(const std::string &paramName, double &minValue, double &maxValue) const
    {
        ErrorCode result = ValidateCamera();
        if (result != ErrorCode::SUCCESS)
        {
            return result;
        }

        return m_pImpl->currentCamera->GetParameterRange(paramName, minValue, maxValue);
    }

    ErrorCode CameraController::ResetToDefault()
    {
        ErrorCode result = ValidateCamera();
        if (result != ErrorCode::SUCCESS)
        {
            return result;
        }

        return m_pImpl->currentCamera->ResetToDefault();
    }

    ErrorCode CameraController::SaveConfiguration(const std::string &filePath)
    {
        ErrorCode result = ValidateCamera();
        if (result != ErrorCode::SUCCESS)
        {
            return result;
        }

        try
        {
            result = m_pImpl->currentCamera->SaveConfiguration(filePath);

            // 触发文件操作回调
            if (m_callbackManager)
            {
                m_callbackManager->TriggerFileOperation("SAVE", filePath, result == ErrorCode::SUCCESS);
            }

            return result;
        }
        catch (const std::exception &e)
        {
            SetError(ErrorCode::FILE_ACCESS_DENIED, e.what());
            return ErrorCode::FILE_ACCESS_DENIED;
        }
    }

    ErrorCode CameraController::LoadConfiguration(const std::string &filePath)
    {
        ErrorCode result = ValidateCamera();
        if (result != ErrorCode::SUCCESS)
        {
            return result;
        }

        try
        {
            result = m_pImpl->currentCamera->LoadConfiguration(filePath);

            // 触发文件操作回调
            if (m_callbackManager)
            {
                m_callbackManager->TriggerFileOperation("LOAD", filePath, result == ErrorCode::SUCCESS);
            }

            return result;
        }
        catch (const std::exception &e)
        {
            SetError(ErrorCode::FILE_NOT_FOUND, e.what());
            return ErrorCode::FILE_NOT_FOUND;
        }
    }

    CameraStatus CameraController::GetCameraStatus() const
    {
        if (!IsCameraConnected())
        {
            return CameraStatus::Disconnected;
        }

        return m_pImpl->currentCamera->GetStatus();
    }

    std::vector<std::string> CameraController::GetSupportedManufacturers() const
    {
        return m_pImpl->GetAllSupportedManufacturers();
    }

    bool CameraController::IsFeatureSupported(const std::string &feature) const
    {
        if (!IsCameraConnected())
        {
            return false;
        }

        return m_pImpl->currentCamera->IsFeatureSupported(feature);
    }

    std::string CameraController::GetLastError() const
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        return m_lastError;
    }

    void CameraController::SetCallbackManager(std::shared_ptr<CallbackManager> callbackManager)
    {
        m_callbackManager = callbackManager;
    }

    void CameraController::SetErrorCallback(std::function<void(ErrorCode, const std::string &)> callback)
    {
        m_errorCallback = callback;
    }

    void CameraController::SetStatusCallback(std::function<void(CameraStatus)> callback)
    {
        m_statusCallback = callback;
    }

    std::string CameraController::GetCurrentManufacturer() const
    {
        return m_pImpl->currentManufacturer;
    }

    std::string CameraController::GetCurrentModel() const
    {
        if (!IsCameraConnected())
        {
            return "Unknown";
        }

        return m_pImpl->currentCamera->GetModel();
    }

    // 私有方法实现
    void CameraController::SetError(ErrorCode error, const std::string &message)
    {
        {
            std::lock_guard<std::mutex> lock(m_mutex);
            m_lastError = message;
        }

        if (m_errorCallback)
        {
            m_errorCallback(error, message);
        }

        if (m_callbackManager)
        {
            m_callbackManager->TriggerError(error, message);
        }
    }

    std::string CameraController::AutoDetectManufacturer(const std::string &deviceId) const
    {
        // 尝试通过设备ID前缀识别厂商
        if (deviceId.find("HV_") == 0 || deviceId.find("Hikvision") != std::string::npos)
        {
            return "Hikvision";
        }
        else if (deviceId.find("Basler") != std::string::npos || deviceId.find("acA") == 0)
        {
            return "Basler";
        }
        else if (deviceId.find("Dahua") != std::string::npos || deviceId.find("DH_") == 0)
        {
            return "Dahua";
        }

        // 如果无法通过ID识别，尝试扫描所有厂商的设备
        for (auto &factory : m_pImpl->factories)
        {
            auto devices = factory->ScanDevices();
            for (const auto &device : devices)
            {
                if (device.deviceId == deviceId)
                {
                    return device.manufacturer;
                }
            }
        }

        return ""; // 无法识别
    }

    std::unique_ptr<ICameraFactory> CameraController::CreateCameraFactory(const std::string &manufacturer) const
    {
        if (manufacturer == "Hikvision")
        {
            return std::make_unique<HikvisionCameraFactory>();
        }
        // 可以在这里添加其他厂商的工厂创建
        // else if (manufacturer == "Basler") {
        //     return std::make_unique<BaslerCameraFactory>();
        // }

        return nullptr;
    }

    ErrorCode CameraController::ValidateCamera() const
    {
        if (!IsInitialized())
        {
            return ErrorCode::SYSTEM_NOT_INITIALIZED;
        }

        if (!IsCameraConnected())
        {
            return ErrorCode::CAMERA_NOT_FOUND;
        }

        return ErrorCode::SUCCESS;
    }

} // namespace VisionModule
