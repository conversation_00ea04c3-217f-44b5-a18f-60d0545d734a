{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-Debug-4eaadb31e4e4e41eaa17.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "vision_widgets", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "vision_widgets::@6890427a1f51a3e7e1df", "jsonFile": "target-vision_widgets-Debug-174e22ba3de967e86320.json", "name": "vision_widgets", "projectIndex": 0}, {"directoryIndex": 0, "id": "vision_widgets_autogen::@6890427a1f51a3e7e1df", "jsonFile": "target-vision_widgets_autogen-Debug-c8db889d2cf1cbc849bb.json", "name": "vision_widgets_autogen", "projectIndex": 0}, {"directoryIndex": 0, "id": "vision_widgets_autogen_timestamp_deps::@6890427a1f51a3e7e1df", "jsonFile": "target-vision_widgets_autogen_timestamp_deps-Debug-2f65b04a37c48b9b21cf.json", "name": "vision_widgets_autogen_timestamp_deps", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "F:/Project/QT_Projact/qt/delta/vison_v4/qt/vision_widgets/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug", "source": "F:/Project/QT_Projact/qt/delta/vison_v4/qt/vision_widgets"}, "version": {"major": 2, "minor": 7}}