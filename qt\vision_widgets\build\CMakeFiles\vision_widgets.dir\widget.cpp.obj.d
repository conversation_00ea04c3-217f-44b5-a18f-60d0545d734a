CMakeFiles/vision_widgets.dir/widget.cpp.obj: \
 F:\Project\QT_Projact\qt\delta\vison_v4\qt\vision_widgets\widget.cpp \
 F:\Project\QT_Projact\qt\delta\vison_v4\qt\vision_widgets\widget.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtWidgets/QWidget \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtWidgets/qwidget.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtWidgets/qtwidgetsglobal.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtGui/qtguiglobal.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qglobal.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/type_traits \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/x86_64-w64-mingw32/bits/c++config.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/x86_64-w64-mingw32/bits/os_defines.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/x86_64-w64-mingw32/bits/cpu_defines.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/pstl/pstl_config.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/cstddef \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/stddef.h \
 D:/Qt_5.15/Tools/mingw1120_64/x86_64-w64-mingw32/include/stddef.h \
 D:/Qt_5.15/Tools/mingw1120_64/x86_64-w64-mingw32/include/crtdefs.h \
 D:/Qt_5.15/Tools/mingw1120_64/x86_64-w64-mingw32/include/corecrt.h \
 D:/Qt_5.15/Tools/mingw1120_64/x86_64-w64-mingw32/include/_mingw.h \
 D:/Qt_5.15/Tools/mingw1120_64/x86_64-w64-mingw32/include/_mingw_mac.h \
 D:/Qt_5.15/Tools/mingw1120_64/x86_64-w64-mingw32/include/_mingw_secapi.h \
 D:/Qt_5.15/Tools/mingw1120_64/x86_64-w64-mingw32/include/vadefs.h \
 D:/Qt_5.15/Tools/mingw1120_64/x86_64-w64-mingw32/include/sdks/_mingw_ddk.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/utility \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/stl_relops.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/stl_pair.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/move.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/initializer_list \
 D:/Qt_5.15/Tools/mingw1120_64/x86_64-w64-mingw32/include/assert.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/stdlib.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/cstdlib \
 D:/Qt_5.15/Tools/mingw1120_64/x86_64-w64-mingw32/include/stdlib.h \
 D:/Qt_5.15/Tools/mingw1120_64/x86_64-w64-mingw32/include/corecrt_wstdlib.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include-fixed/limits.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include-fixed/syslimits.h \
 D:/Qt_5.15/Tools/mingw1120_64/x86_64-w64-mingw32/include/limits.h \
 D:/Qt_5.15/Tools/mingw1120_64/x86_64-w64-mingw32/include/sec_api/stdlib_s.h \
 D:/Qt_5.15/Tools/mingw1120_64/x86_64-w64-mingw32/include/malloc.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/mm_malloc.h \
 D:/Qt_5.15/Tools/mingw1120_64/x86_64-w64-mingw32/include/errno.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/std_abs.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qconfig.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qtcore-config.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qsystemdetection.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qprocessordetection.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qcompilerdetection.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/algorithm \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/stl_algobase.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/functexcept.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/exception_defines.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/cpp_type_traits.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/ext/type_traits.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/ext/numeric_traits.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/stl_iterator_base_types.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/stl_iterator_base_funcs.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/concept_check.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/debug/assertions.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/stl_iterator.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/ptr_traits.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/debug/debug.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/predefined_ops.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/stl_algo.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/algorithmfwd.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/stl_heap.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/stl_tempbuf.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/stl_construct.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/new \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/exception.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/uniform_int_dist.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/pstl/glue_algorithm_defs.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/functional \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/stl_function.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/backward/binders.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/tuple \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/array \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/range_access.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/uses_allocator.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/invoke.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/functional_hash.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/hash_bytes.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/refwrap.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/std_function.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/typeinfo \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/unordered_map \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/allocator.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/x86_64-w64-mingw32/bits/c++allocator.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/ext/new_allocator.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/memoryfwd.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/ext/alloc_traits.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/alloc_traits.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/ext/aligned_buffer.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/hashtable.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/hashtable_policy.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/node_handle.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/unordered_map.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/erase_if.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/vector \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/stl_uninitialized.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/stl_vector.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/stl_bvector.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/vector.tcc \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/pstl/execution_defs.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qtypeinfo.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qsysinfo.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qlogging.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qflags.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qatomic.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qbasicatomic.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qatomic_cxx11.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qgenericatomic.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/atomic \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/atomic_base.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/stdint.h \
 D:/Qt_5.15/Tools/mingw1120_64/x86_64-w64-mingw32/include/stdint.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/atomic_lockfree_defines.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qglobalstatic.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qnumeric.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qversiontagging.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtGui/qtgui-config.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtWidgets/qtwidgets-config.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtGui/qwindowdefs.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qobjectdefs.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qnamespace.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qobjectdefs_impl.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtGui/qwindowdefs_win.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qobject.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qstring.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qchar.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qbytearray.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qrefcount.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qarraydata.h \
 D:/Qt_5.15/Tools/mingw1120_64/x86_64-w64-mingw32/include/string.h \
 D:/Qt_5.15/Tools/mingw1120_64/x86_64-w64-mingw32/include/sec_api/string_s.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/stdarg.h \
 D:/Qt_5.15/Tools/mingw1120_64/x86_64-w64-mingw32/include/stdarg.h \
 D:/Qt_5.15/Tools/mingw1120_64/x86_64-w64-mingw32/include/_mingw_stdarg.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/string \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/stringfwd.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/char_traits.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/postypes.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/cwchar \
 D:/Qt_5.15/Tools/mingw1120_64/x86_64-w64-mingw32/include/wchar.h \
 D:/Qt_5.15/Tools/mingw1120_64/x86_64-w64-mingw32/include/corecrt_stdio_config.h \
 D:/Qt_5.15/Tools/mingw1120_64/x86_64-w64-mingw32/include/_mingw_off_t.h \
 D:/Qt_5.15/Tools/mingw1120_64/x86_64-w64-mingw32/include/_mingw_stat64.h \
 D:/Qt_5.15/Tools/mingw1120_64/x86_64-w64-mingw32/include/swprintf.inl \
 D:/Qt_5.15/Tools/mingw1120_64/x86_64-w64-mingw32/include/sec_api/wchar_s.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/cstdint \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/localefwd.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/x86_64-w64-mingw32/bits/c++locale.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/clocale \
 D:/Qt_5.15/Tools/mingw1120_64/x86_64-w64-mingw32/include/locale.h \
 D:/Qt_5.15/Tools/mingw1120_64/x86_64-w64-mingw32/include/stdio.h \
 D:/Qt_5.15/Tools/mingw1120_64/x86_64-w64-mingw32/include/sec_api/stdio_s.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/iosfwd \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/cctype \
 D:/Qt_5.15/Tools/mingw1120_64/x86_64-w64-mingw32/include/ctype.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/ostream_insert.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/cxxabi_forced.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/basic_string.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/ext/atomicity.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/x86_64-w64-mingw32/bits/gthr.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/x86_64-w64-mingw32/bits/gthr-default.h \
 D:/Qt_5.15/Tools/mingw1120_64/x86_64-w64-mingw32/include/pthread.h \
 D:/Qt_5.15/Tools/mingw1120_64/x86_64-w64-mingw32/include/sys/types.h \
 D:/Qt_5.15/Tools/mingw1120_64/x86_64-w64-mingw32/include/process.h \
 D:/Qt_5.15/Tools/mingw1120_64/x86_64-w64-mingw32/include/corecrt_startup.h \
 D:/Qt_5.15/Tools/mingw1120_64/x86_64-w64-mingw32/include/signal.h \
 D:/Qt_5.15/Tools/mingw1120_64/x86_64-w64-mingw32/include/pthread_signal.h \
 D:/Qt_5.15/Tools/mingw1120_64/x86_64-w64-mingw32/include/time.h \
 D:/Qt_5.15/Tools/mingw1120_64/x86_64-w64-mingw32/include/sys/timeb.h \
 D:/Qt_5.15/Tools/mingw1120_64/x86_64-w64-mingw32/include/sec_api/sys/timeb_s.h \
 D:/Qt_5.15/Tools/mingw1120_64/x86_64-w64-mingw32/include/_timeval.h \
 D:/Qt_5.15/Tools/mingw1120_64/x86_64-w64-mingw32/include/pthread_time.h \
 D:/Qt_5.15/Tools/mingw1120_64/x86_64-w64-mingw32/include/pthread_compat.h \
 D:/Qt_5.15/Tools/mingw1120_64/x86_64-w64-mingw32/include/pthread_unistd.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/x86_64-w64-mingw32/bits/atomic_word.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/string_view \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/string_view.tcc \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/ext/string_conversions.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/cstdio \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/cerrno \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/charconv.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/basic_string.tcc \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/iterator \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/stream_iterator.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/streambuf_iterator.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/streambuf \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/ios_base.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/locale_classes.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/locale_classes.tcc \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/system_error \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/x86_64-w64-mingw32/bits/error_constants.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/stdexcept \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/exception \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/exception_ptr.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/cxxabi_init_exception.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/nested_exception.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/streambuf.tcc \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qstringliteral.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qstringalgorithms.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qstringview.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qlist.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qalgorithms.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qiterator.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qhashfunctions.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qpair.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/numeric \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/stl_numeric.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bit \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/pstl/glue_numeric_defs.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qvector.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qcontainertools_impl.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/list \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/stl_list.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/allocated_ptr.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/list.tcc \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qbytearraylist.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qstringlist.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qregexp.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qstringmatcher.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qscopedpointer.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qmetatype.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qvarlengtharray.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qcontainerfwd.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/map \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/stl_tree.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/stl_map.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/stl_multimap.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qobject_impl.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/chrono \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/ratio \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/limits \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/ctime \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/parse_numbers.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qmargins.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtGui/qpaintdevice.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qrect.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qsize.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qpoint.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtGui/qpalette.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtGui/qcolor.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtGui/qrgb.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtGui/qrgba64.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtGui/qbrush.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtGui/qmatrix.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtGui/qpolygon.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtGui/qregion.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qdatastream.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qiodevice.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qline.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtGui/qtransform.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtGui/qimage.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtGui/qpixelformat.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtGui/qpixmap.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qsharedpointer.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qshareddata.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qhash.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qsharedpointer_impl.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/memory \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/stl_raw_storage_iter.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/align.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/unique_ptr.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/shared_ptr.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/shared_ptr_base.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/ext/concurrence.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/shared_ptr_atomic.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/backward/auto_ptr.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/pstl/glue_memory_defs.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtGui/qfont.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtGui/qfontmetrics.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtGui/qfontinfo.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtWidgets/qsizepolicy.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtGui/qcursor.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtGui/qkeysequence.h \
 F:/Project/QT_Projact/qt/delta/vison_v4/qt/vision_widgets/build/vision_widgets_autogen/include/ui_widget.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/QVariant \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qvariant.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qmap.h \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/variant \
 D:/Qt_5.15/Tools/mingw1120_64/lib/gcc/x86_64-w64-mingw32/11.2.0/include/c++/bits/enable_special_members.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtWidgets/QApplication \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtWidgets/qapplication.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qcoreapplication.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qcoreevent.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qeventloop.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtGui/qguiapplication.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtGui/qinputmethod.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qlocale.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qvariant.h \
 D:/Qt_5.15/5.15.2/mingw81_64/include/QtWidgets/QWidget
