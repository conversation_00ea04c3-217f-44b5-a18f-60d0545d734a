{"cmake": {"generator": {"multiConfig": false, "name": "MinGW Makefiles"}, "paths": {"cmake": "D:/Qt_5.15/Tools/CMake_64/bin/cmake.exe", "cpack": "D:/Qt_5.15/Tools/CMake_64/bin/cpack.exe", "ctest": "D:/Qt_5.15/Tools/CMake_64/bin/ctest.exe", "root": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30"}, "version": {"isDirty": false, "major": 3, "minor": 30, "patch": 5, "string": "3.30.5", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-0374651d538299d701c6.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}, {"jsonFile": "cache-v2-5e0331d5aeccec735dd6.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-8c13682e134a42d4e08c.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}], "reply": {"cache-v2": {"jsonFile": "cache-v2-5e0331d5aeccec735dd6.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-8c13682e134a42d4e08c.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, "codemodel-v2": {"jsonFile": "codemodel-v2-0374651d538299d701c6.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}}}