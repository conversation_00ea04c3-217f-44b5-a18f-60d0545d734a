{"version": "2.0.0", "tasks": [{"label": "CMake Configure", "type": "shell", "command": "cmake", "args": ["-B", "${workspaceFolder}/qt/vision_widgets/build", "-S", "${workspaceFolder}/qt/vision_widgets", "-G", "MinGW Makefiles", "-DCMAKE_BUILD_TYPE=Debug", "-DCMAKE_PREFIX_PATH=D:/Qt_5.15/5.15.2/mingw81_64"], "group": "build", "problemMatcher": []}, {"label": "CMake Build", "type": "shell", "command": "cmake", "args": ["--build", "${workspaceFolder}/qt/vision_widgets/build", "--config", "Debug", "-j", "8"], "group": {"kind": "build", "isDefault": true}, "dependsOn": ["CMake Configure"], "problemMatcher": "$gcc"}]}