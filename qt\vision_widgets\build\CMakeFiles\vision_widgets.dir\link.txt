D:\Qt_5.15\Tools\CMake_64\bin\cmake.exe -E rm -f CMakeFiles\vision_widgets.dir/objects.a
D:\Qt_5.15\Tools\mingw1120_64\bin\ar.exe qc CMakeFiles\vision_widgets.dir/objects.a @CMakeFiles\vision_widgets.dir\objects1.rsp
D:\Qt_5.15\Tools\mingw1120_64\bin\c++.exe -g -mwindows -Wl,--whole-archive CMakeFiles\vision_widgets.dir/objects.a -Wl,--no-whole-archive -o vision_widgets.exe -Wl,--out-implib,libvision_widgets.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\vision_widgets.dir\linkLibs.rsp
