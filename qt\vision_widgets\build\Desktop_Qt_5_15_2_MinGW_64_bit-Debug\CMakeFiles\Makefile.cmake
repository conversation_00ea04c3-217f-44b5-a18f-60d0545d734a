# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.30

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "MinGW Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5/Qt5Config.cmake"
  "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5/Qt5ConfigVersion.cmake"
  "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5/Qt5ModuleLocation.cmake"
  "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Core/Qt5CoreConfig.cmake"
  "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Core/Qt5CoreConfigExtras.cmake"
  "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Core/Qt5CoreConfigExtrasMkspecDir.cmake"
  "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Core/Qt5CoreConfigVersion.cmake"
  "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Core/Qt5CoreMacros.cmake"
  "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Gui/Qt5GuiConfig.cmake"
  "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Gui/Qt5GuiConfigExtras.cmake"
  "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Gui/Qt5GuiConfigVersion.cmake"
  "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QGifPlugin.cmake"
  "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QICNSPlugin.cmake"
  "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QICOPlugin.cmake"
  "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QJpegPlugin.cmake"
  "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QMinimalIntegrationPlugin.cmake"
  "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QOffscreenIntegrationPlugin.cmake"
  "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QSvgIconPlugin.cmake"
  "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QSvgPlugin.cmake"
  "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QTgaPlugin.cmake"
  "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QTiffPlugin.cmake"
  "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QTuioTouchPlugin.cmake"
  "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QWbmpPlugin.cmake"
  "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QWebpPlugin.cmake"
  "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QWindowsDirect2DIntegrationPlugin.cmake"
  "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QWindowsIntegrationPlugin.cmake"
  "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QXdgDesktopPortalThemePlugin.cmake"
  "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5LinguistTools/Qt5LinguistToolsConfig.cmake"
  "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5LinguistTools/Qt5LinguistToolsConfigVersion.cmake"
  "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5LinguistTools/Qt5LinguistToolsMacros.cmake"
  "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Widgets/Qt5WidgetsConfig.cmake"
  "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Widgets/Qt5WidgetsConfigExtras.cmake"
  "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Widgets/Qt5WidgetsConfigVersion.cmake"
  "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Widgets/Qt5WidgetsMacros.cmake"
  "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Widgets/Qt5Widgets_QWindowsVistaStylePlugin.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeCXXCompiler.cmake.in"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeCXXCompilerABI.cpp"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeCXXInformation.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeCommonLanguageInclude.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeCompilerIdDetection.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompiler.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerSupport.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineRCCompiler.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineSystem.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeFindBinUtils.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeGenericSystem.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeInitializeConfigs.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeLanguageInformation.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeMinGWFindMake.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeParseArguments.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeParseImplicitIncludeInfo.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeParseImplicitLinkInfo.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeParseLibraryArchitecture.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeRCCompiler.cmake.in"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeRCInformation.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeSystem.cmake.in"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeSystemSpecificInformation.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeSystemSpecificInitialize.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeTestCXXCompiler.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeTestCompilerCommon.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeTestRCCompiler.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/ADSP-DetermineCompiler.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/ARMCC-DetermineCompiler.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/ARMClang-DetermineCompiler.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/AppleClang-DetermineCompiler.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/Borland-DetermineCompiler.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/Clang-DetermineCompiler.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/Cray-DetermineCompiler.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/CrayClang-DetermineCompiler.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/Embarcadero-DetermineCompiler.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/Fujitsu-DetermineCompiler.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/GHS-DetermineCompiler.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/GNU-CXX.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/GNU-FindBinUtils.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/GNU.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/HP-CXX-DetermineCompiler.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/IAR-DetermineCompiler.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/IBMClang-CXX-DetermineCompiler.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/Intel-DetermineCompiler.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/LCC-CXX-DetermineCompiler.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/MSVC-DetermineCompiler.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/NVHPC-DetermineCompiler.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/NVIDIA-DetermineCompiler.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/OrangeC-DetermineCompiler.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/PGI-DetermineCompiler.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/PathScale-DetermineCompiler.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/SCO-DetermineCompiler.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/TI-DetermineCompiler.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/TIClang-DetermineCompiler.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/Tasking-DetermineCompiler.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/Watcom-DetermineCompiler.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/XL-CXX-DetermineCompiler.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/GNUInstallDirs.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Internal/CMakeDetermineLinkerId.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Internal/FeatureTesting.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows-Determine-CXX.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows-GNU-CXX-ABI.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows-GNU-CXX.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows-GNU.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows-Initialize.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows-windres.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows.cmake"
  "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Platform/WindowsPaths.cmake"
  "F:/Project/QT_Projact/qt/delta/vison_v4/qt/vision_widgets/CMakeLists.txt"
  ".qtc/package-manager/auto-setup.cmake"
  "CMakeFiles/3.30.5/CMakeCXXCompiler.cmake"
  "CMakeFiles/3.30.5/CMakeRCCompiler.cmake"
  "CMakeFiles/3.30.5/CMakeSystem.cmake"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "CMakeFiles/3.30.5/CMakeSystem.cmake"
  "CMakeFiles/3.30.5/CMakeCXXCompiler.cmake"
  "CMakeFiles/3.30.5/CMakeRCCompiler.cmake"
  "CMakeFiles/3.30.5/CMakeCXXCompiler.cmake"
  "CMakeFiles/vision_widgets_autogen.dir/AutogenInfo.json"
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "CMakeFiles/vision_widgets.dir/DependInfo.cmake"
  "CMakeFiles/vision_widgets_autogen_timestamp_deps.dir/DependInfo.cmake"
  "CMakeFiles/vision_widgets_autogen.dir/DependInfo.cmake"
  )
