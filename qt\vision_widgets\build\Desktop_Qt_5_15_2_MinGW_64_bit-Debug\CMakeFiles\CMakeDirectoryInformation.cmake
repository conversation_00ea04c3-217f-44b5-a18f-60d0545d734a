# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.30

# Relative path conversion top directories.
set(CMAKE_RELATIVE_PATH_TOP_SOURCE "F:/Project/QT_Projact/qt/delta/vison_v4/qt/vision_widgets")
set(CMAKE_RELATIVE_PATH_TOP_BINARY "F:/Project/QT_Projact/qt/delta/vison_v4/qt/vision_widgets/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug")

# Force unix paths in dependencies.
set(CMAKE_FORCE_UNIX_PATHS 1)


# The C and CXX include file regular expressions for this directory.
set(CMAKE_C_INCLUDE_REGEX_SCAN "^.*$")
set(CMAKE_C_INCLUDE_REGEX_COMPLAIN "^$")
set(CMAKE_CXX_INCLUDE_REGEX_SCAN ${CMAKE_C_INCLUDE_REGEX_SCAN})
set(CMAKE_CXX_INCLUDE_REGEX_COMPLAIN ${CMAKE_C_INCLUDE_REGEX_COMPLAIN})
