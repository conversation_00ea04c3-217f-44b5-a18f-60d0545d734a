# 主窗口UI布局规范 (增强版)

## 1. 整体布局设计

### 1.1 布局架构

采用**Dock窗口+中心区域**的布局方式，符合工业软件操作习惯，支持窗口的自由拖拽和停靠。

### 1.2 响应式设计

- **最小窗口尺寸**: 1024x768px
- **推荐窗口尺寸**: 1920x1080px
- **支持高DPI显示**: 自动缩放适配
- **多显示器支持**: 可跨屏显示

## 2. 功能分区详细设计

### 2.1 顶部工具栏 (ToolBar)

#### 2.1.1 文件操作区

- **新建项目** (Ctrl+N): 创建新的视觉项目
- **打开项目** (Ctrl+O): 加载已有项目文件
- **保存项目** (Ctrl+S): 保存当前项目配置
- **导入配置**: 导入标定数据和模板文件
- **导出结果**: 导出坐标数据和报告

#### 2.1.2 相机控制区

- **连接相机**: 自动检测并连接可用相机
- **断开相机**: 安全断开相机连接
- **单次拍照**: 触发一次图像采集
- **连续采集**: 开启/停止实时图像流
- **相机设置**: 打开相机参数配置对话框

#### 2.1.3 标定功能区

- **开始标定**: 启动标定向导流程
- **加载标定**: 加载已有标定文件
- **验证标定**: 验证当前标定精度
- **重置标定**: 清除当前标定数据

#### 2.1.4 模板功能区

- **创建模板**: 在当前图像上创建新模板
- **加载模板**: 加载已保存的模板文件
- **开始匹配**: 启动实时模板匹配
- **停止匹配**: 停止模板匹配过程

### 2.2 左侧Dock窗口 (Left Panel)

#### 2.2.1 相机参数面板

- **曝光时间控制**:
  - 滑块范围: 1-100ms
  - 数值输入框: 支持小数点后2位
  - 自动曝光按钮: 一键优化曝光
- **增益控制**:
  - 滑块范围: 1.0-15.0
  - 实时预览效果
  - 增益锁定功能
- **其他参数**:
  - 白平衡调节
  - 图像格式选择
  - 触发模式设置

#### 2.2.2 标定参数面板

- **标定点管理**:
  - 标定点列表显示
  - 坐标编辑功能
  - 点位删除和重新选择
- **标定状态显示**:
  - 当前标定精度
  - 标定点数量统计
  - 标定质量评估

### 2.3 右侧Dock窗口 (Right Panel)

#### 2.3.1 模板参数面板

  - 最大匹配数量: 1-50
  - 角度搜索范围: 0-360°
  - 缩放搜索范围: 0.5-2.0
- **模板信息显示**:
  - 模板尺寸和特征点数
  - 模板创建时间
  - 模板质量评分

#### 2.3.2 结果输出面板

- **坐标数据表格**:
  - 实时显示匹配结果
  - 支持数据排序和筛选
  - 一键复制和导出功能
- **统计信息**:
  - 匹配成功率
  - 平均匹配时间
  - 坐标精度统计

### 2.4 中心区域 (Central Widget)

#### 2.4.1 图像显示区
- **图像渲染**:
  - 支持多种图像格式
  - 硬件加速渲染
  - 保持图像宽高比
- **交互功能**:
  - 鼠标滚轮缩放
  - 拖拽平移
  - 双击自适应显示
  - 右键菜单操作
- **叠加显示**:
  - 标定点标记
  - 匹配结果轮廓
  - 坐标系显示
  - ROI区域高亮

#### 2.4.2 状态栏
- **系统状态指示器**:
  - 相机连接状态
  - 标定状态
  - 匹配状态
- **实时信息显示**:
  - 当前图像尺寸
  - 鼠标位置坐标
  - 系统资源占用
- **操作提示**:
  - 当前操作步骤提示
  - 快捷键提示
  - 错误信息显示

## 3. 控件规格与样式

### 3.1 控件尺寸规范

| 控件类型 | 尺寸       | 字体   | 颜色     | 备注           |
|----------|------------|--------|----------|----------------|
| 工具栏按钮 | 32x32px    | 9pt    | #2C3E50  | 带图标和文字   |
| 参数滑块 | 150x20px   | 8pt    | #34495E  | 双精度浮点     |
| 输入框   | 80x24px    | 9pt    | #FFFFFF  | 支持数值验证   |
| 标签文字 | 自适应     | 9pt    | #2C3E50  | 左对齐         |
| 按钮     | 80x28px    | 9pt    | #3498DB  | 圆角3px        |

### 3.2 颜色主题

#### 3.2.1 主色调
- **主色**: #3498DB (蓝色)
- **辅助色**: #2ECC71 (绿色)
- **警告色**: #F39C12 (橙色)
- **错误色**: #E74C3C (红色)

#### 3.2.2 背景色
- **主背景**: #ECF0F1 (浅灰)
- **面板背景**: #FFFFFF (白色)
- **工具栏背景**: #BDC3C7 (中灰)

### 3.3 图标设计规范
- **图标尺寸**: 16x16px, 24x24px, 32x32px
- **图标风格**: 扁平化设计，线条清晰
- **图标颜色**: 与主题色调保持一致

## 4. 交互流程与状态管理

### 4.1 操作流程
1. **初始化**: 启动程序 → 检查硬件 → 加载配置
2. **相机连接**: 检测设备 → 建立连接 → 参数初始化
3. **图像采集**: 设置参数 → 开始采集 → 实时显示
4. **系统标定**: 放置标定板 → 选择标定点 → 计算变换矩阵
5. **模板创建**: 采集样本 → 定义ROI → 生成模板
6. **实时匹配**: 加载模板 → 设置参数 → 开始匹配
7. **结果输出**: 坐标转换 → 数据显示 → 导出结果

### 4.2 状态管理
- **未连接状态**: 相机相关功能禁用
- **已连接状态**: 启用图像采集功能
- **标定状态**: 启用坐标转换功能
- **匹配状态**: 启用结果输出功能

### 4.3 错误处理界面
- **错误对话框**: 显示详细错误信息和解决建议
- **状态指示器**: 实时显示系统运行状态
- **日志窗口**: 记录详细的操作日志

## 5. 可访问性与用户体验

### 5.1 键盘快捷键
- **Ctrl+N**: 新建项目
- **Ctrl+O**: 打开项目
- **Ctrl+S**: 保存项目
- **Space**: 单次拍照
- **F5**: 开始/停止连续采集
- **F11**: 全屏显示

### 5.2 用户体验优化
- **操作反馈**: 所有操作都有明确的视觉反馈
- **进度指示**: 长时间操作显示进度条
- **撤销重做**: 支持关键操作的撤销和重做
- **自动保存**: 定期自动保存项目状态

### 5.3 帮助系统
- **工具提示**: 鼠标悬停显示功能说明
- **帮助文档**: 内置用户手册和FAQ
- **视频教程**: 关键操作的视频演示