#ifndef ERRORCODES_H
#define ERRORCODES_H

/**
 * @file ErrorCodes.h
 * @brief 视觉模块错误码定义
 * <AUTHOR> Module Team
 * @version 1.0.0
 * @date 2025-07-04
 */

namespace VisionModule {

/**
 * @brief 错误码枚举
 * 
 * 错误码分类：
 * - 1000-1099: 系统初始化错误
 * - 1100-1199: 相机相关错误
 * - 1200-1299: 标定相关错误
 * - 1300-1399: 模板匹配错误
 * - 1400-1499: 坐标转换错误
 */
enum class ErrorCode : int {
    // 成功
    SUCCESS = 0,
    
    // 系统错误 (1000-1099)
    SYSTEM_NOT_INITIALIZED = 1000,     ///< 系统未初始化
    INVALID_PARAMETERS = 1001,          ///< 参数无效
    MEMORY_ALLOCATION_FAILED = 1002,    ///< 内存分配失败
    FILE_NOT_FOUND = 1003,              ///< 文件未找到
    FILE_ACCESS_DENIED = 1004,          ///< 文件访问被拒绝
    FILE_FORMAT_ERROR = 1005,           ///< 文件格式错误
    CONFIGURATION_ERROR = 1006,         ///< 配置错误
    LIBRARY_LOAD_FAILED = 1007,         ///< 库加载失败
    LICENSE_ERROR = 1008,               ///< 许可证错误
    TIMEOUT_ERROR = 1009,               ///< 超时错误
    
    // 相机错误 (1100-1199)
    CAMERA_NOT_FOUND = 1100,            ///< 相机未找到
    CAMERA_CONNECTION_FAILED = 1101,    ///< 相机连接失败
    CAMERA_DISCONNECTED = 1102,         ///< 相机已断开
    CAMERA_BUSY = 1103,                 ///< 相机忙碌
    CAMERA_PARAMETER_INVALID = 1104,    ///< 相机参数无效
    CAMERA_CAPTURE_FAILED = 1105,       ///< 图像采集失败
    CAMERA_TIMEOUT = 1106,              ///< 相机超时
    CAMERA_BUFFER_OVERFLOW = 1107,      ///< 相机缓冲区溢出
    CAMERA_SDK_ERROR = 1108,            ///< 相机SDK错误
    CAMERA_PERMISSION_DENIED = 1109,    ///< 相机权限被拒绝
    
    // 标定错误 (1200-1299)
    CALIBRATION_NOT_READY = 1200,           ///< 标定未就绪
    CALIBRATION_POINTS_INSUFFICIENT = 1201, ///< 标定点不足
    CALIBRATION_CALCULATION_FAILED = 1202,  ///< 标定计算失败
    CALIBRATION_DATA_INVALID = 1203,        ///< 标定数据无效
    CALIBRATION_ACCURACY_LOW = 1204,        ///< 标定精度过低
    CALIBRATION_MATRIX_SINGULAR = 1205,     ///< 标定矩阵奇异
    CALIBRATION_POINTS_COLLINEAR = 1206,    ///< 标定点共线
    CALIBRATION_SAVE_FAILED = 1207,         ///< 标定保存失败
    CALIBRATION_LOAD_FAILED = 1208,         ///< 标定加载失败
    
    // 模板匹配错误 (1300-1399)
    TEMPLATE_NOT_FOUND = 1300,          ///< 模板未找到
    TEMPLATE_CREATION_FAILED = 1301,    ///< 模板创建失败
    TEMPLATE_MATCHING_FAILED = 1302,    ///< 模板匹配失败
    TEMPLATE_DATA_INVALID = 1303,       ///< 模板数据无效
    TEMPLATE_ROI_INVALID = 1304,        ///< ROI区域无效
    TEMPLATE_SAVE_FAILED = 1305,        ///< 模板保存失败
    TEMPLATE_LOAD_FAILED = 1306,        ///< 模板加载失败
    TEMPLATE_QUALITY_LOW = 1307,        ///< 模板质量过低
    TEMPLATE_SIZE_INVALID = 1308,       ///< 模板尺寸无效
    
    // 坐标转换错误 (1400-1499)
    COORDINATE_TRANSFORM_FAILED = 1400,     ///< 坐标转换失败
    CALIBRATION_MATRIX_INVALID = 1401,      ///< 标定矩阵无效
    COORDINATE_OUT_OF_RANGE = 1402,         ///< 坐标超出范围
    TRANSFORM_MATRIX_SINGULAR = 1403,       ///< 变换矩阵奇异
    PIXEL_COORDINATE_INVALID = 1404,        ///< 像素坐标无效
    PHYSICAL_COORDINATE_INVALID = 1405,     ///< 物理坐标无效
    
    // 图像处理错误 (1500-1599)
    IMAGE_EMPTY = 1500,                 ///< 图像为空
    IMAGE_FORMAT_UNSUPPORTED = 1501,    ///< 图像格式不支持
    IMAGE_SIZE_INVALID = 1502,          ///< 图像尺寸无效
    IMAGE_PROCESSING_FAILED = 1503,     ///< 图像处理失败
    IMAGE_SAVE_FAILED = 1504,           ///< 图像保存失败
    IMAGE_LOAD_FAILED = 1505,           ///< 图像加载失败
    
    // 算法错误 (1600-1699)
    ALGORITHM_NOT_INITIALIZED = 1600,   ///< 算法未初始化
    ALGORITHM_EXECUTION_FAILED = 1601,  ///< 算法执行失败
    ALGORITHM_PARAMETER_INVALID = 1602, ///< 算法参数无效
    ALGORITHM_CONVERGENCE_FAILED = 1603,///< 算法收敛失败
    
    // 硬件错误 (1700-1799)
    HARDWARE_NOT_FOUND = 1700,          ///< 硬件未找到
    HARDWARE_INITIALIZATION_FAILED = 1701, ///< 硬件初始化失败
    HARDWARE_COMMUNICATION_ERROR = 1702,   ///< 硬件通信错误
    
    // 网络错误 (1800-1899)
    NETWORK_CONNECTION_FAILED = 1800,   ///< 网络连接失败
    NETWORK_TIMEOUT = 1801,             ///< 网络超时
    NETWORK_DATA_CORRUPTED = 1802,      ///< 网络数据损坏
    
    // 未知错误
    UNKNOWN_ERROR = 9999                ///< 未知错误
};

/**
 * @brief 获取错误码对应的错误描述
 * @param code 错误码
 * @return 错误描述字符串
 */
const char* GetErrorDescription(ErrorCode code);

/**
 * @brief 检查错误码是否表示成功
 * @param code 错误码
 * @return true表示成功，false表示失败
 */
inline bool IsSuccess(ErrorCode code) {
    return code == ErrorCode::SUCCESS;
}

/**
 * @brief 检查错误码是否表示失败
 * @param code 错误码
 * @return true表示失败，false表示成功
 */
inline bool IsError(ErrorCode code) {
    return code != ErrorCode::SUCCESS;
}

/**
 * @brief 获取错误码的分类
 * @param code 错误码
 * @return 错误分类字符串
 */
const char* GetErrorCategory(ErrorCode code);

} // namespace VisionModule

#endif // ERRORCODES_H
