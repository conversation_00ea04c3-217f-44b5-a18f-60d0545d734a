# Generated by CMake. Changes will be overwritten.
F:/Project/QT_Projact/qt/delta/vison_v4/qt/vision_widgets/main.cpp
F:/Project/QT_Projact/qt/delta/vison_v4/qt/vision_widgets/widget.h
 mmc:Q_OBJECT
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qalgorithms.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qarraydata.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qatomic.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qatomic_cxx11.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qbasicatomic.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qbytearray.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qbytearraylist.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qchar.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qcompilerdetection.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qconfig.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qcontainerfwd.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qcontainertools_impl.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qdatastream.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qflags.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qgenericatomic.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qglobal.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qglobalstatic.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qhash.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qhashfunctions.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qiodevice.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qiterator.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qline.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qlist.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qlogging.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qmargins.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qmetatype.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qnamespace.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qnumeric.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qobject.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qobject_impl.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qobjectdefs.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qobjectdefs_impl.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qpair.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qpoint.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qprocessordetection.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qrect.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qrefcount.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qregexp.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qscopedpointer.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qshareddata.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qsharedpointer.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qsharedpointer_impl.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qsize.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qstring.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qstringalgorithms.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qstringlist.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qstringliteral.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qstringmatcher.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qstringview.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qsysinfo.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qsystemdetection.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qtcore-config.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qtypeinfo.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qvarlengtharray.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qvector.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtCore/qversiontagging.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtGui/qbrush.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtGui/qcolor.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtGui/qcursor.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtGui/qfont.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtGui/qfontinfo.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtGui/qfontmetrics.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtGui/qimage.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtGui/qkeysequence.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtGui/qmatrix.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtGui/qpaintdevice.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtGui/qpalette.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtGui/qpixelformat.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtGui/qpixmap.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtGui/qpolygon.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtGui/qregion.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtGui/qrgb.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtGui/qrgba64.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtGui/qtgui-config.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtGui/qtguiglobal.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtGui/qtransform.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtGui/qwindowdefs.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtGui/qwindowdefs_win.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtWidgets/QWidget
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtWidgets/qsizepolicy.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtWidgets/qtwidgets-config.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtWidgets/qtwidgetsglobal.h
 mdp:D:/Qt_5.15/5.15.2/mingw81_64/include/QtWidgets/qwidget.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed/limits.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed/syslimits.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/algorithm
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/array
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/atomic
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/backward/auto_ptr.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/backward/binders.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/algorithmfwd.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/alloc_traits.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/allocated_ptr.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/allocator.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/atomic_base.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/atomic_lockfree_defines.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/basic_ios.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/basic_ios.tcc
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/basic_string.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/basic_string.tcc
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/c++0x_warning.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/char_traits.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/concept_check.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/cpp_type_traits.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/cxxabi_forced.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/cxxabi_init_exception.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/enable_special_members.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/exception.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/exception_defines.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/exception_ptr.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/functexcept.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/functional_hash.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/hash_bytes.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/invoke.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/ios_base.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/istream.tcc
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/list.tcc
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/locale_classes.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/locale_classes.tcc
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/locale_facets.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/locale_facets.tcc
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/localefwd.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/memoryfwd.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/move.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/nested_exception.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/node_handle.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/ostream.tcc
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/ostream_insert.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/postypes.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/predefined_ops.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/ptr_traits.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/range_access.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/refwrap.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/shared_ptr.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/shared_ptr_atomic.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/shared_ptr_base.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/std_abs.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/std_function.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/stl_algo.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/stl_algobase.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/stl_bvector.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/stl_construct.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/stl_function.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/stl_heap.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/stl_iterator.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/stl_iterator_base_funcs.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/stl_iterator_base_types.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/stl_list.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/stl_map.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/stl_multimap.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/stl_numeric.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/stl_pair.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/stl_raw_storage_iter.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/stl_relops.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/stl_tempbuf.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/stl_tree.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/stl_uninitialized.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/stl_vector.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/stream_iterator.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/streambuf.tcc
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/streambuf_iterator.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/string_view.tcc
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/stringfwd.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/uniform_int_dist.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/unique_ptr.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/uses_allocator.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/vector.tcc
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/cctype
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/clocale
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/cstddef
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/cstdint
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/cstdlib
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/cwchar
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/cwctype
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/debug/assertions.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/debug/debug.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/exception
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/ext/aligned_buffer.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/ext/alloc_traits.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/ext/atomicity.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/ext/concurrence.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/ext/new_allocator.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/ext/numeric_traits.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/ext/string_conversions.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/ext/type_traits.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/functional
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/initializer_list
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/ios
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/iosfwd
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/istream
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/iterator
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/limits
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/list
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/map
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/memory
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/new
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/numeric
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/optional
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/ostream
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/stdexcept
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/stdlib.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/streambuf
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/string
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/string_view
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/tuple
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/type_traits
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/typeinfo
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/unordered_map
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/utility
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/vector
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32/bits/atomic_word.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32/bits/c++allocator.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32/bits/c++config.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32/bits/c++locale.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32/bits/cpu_defines.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32/bits/ctype_base.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32/bits/ctype_inline.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32/bits/gthr-default.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32/bits/gthr.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32/bits/os_defines.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/stdarg.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/stddef.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/stdint.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/x86_64-w64-mingw32/include/_mingw.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/x86_64-w64-mingw32/include/_mingw_mac.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/x86_64-w64-mingw32/include/_mingw_off_t.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/x86_64-w64-mingw32/include/_mingw_print_pop.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/x86_64-w64-mingw32/include/_mingw_print_push.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/x86_64-w64-mingw32/include/_mingw_secapi.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/x86_64-w64-mingw32/include/_mingw_stat64.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/x86_64-w64-mingw32/include/assert.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/x86_64-w64-mingw32/include/corecrt_startup.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/x86_64-w64-mingw32/include/crtdefs.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/x86_64-w64-mingw32/include/ctype.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/x86_64-w64-mingw32/include/errno.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/x86_64-w64-mingw32/include/locale.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/x86_64-w64-mingw32/include/process.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/x86_64-w64-mingw32/include/pthread.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/x86_64-w64-mingw32/include/pthread_compat.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/x86_64-w64-mingw32/include/pthread_signal.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/x86_64-w64-mingw32/include/pthread_unistd.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/x86_64-w64-mingw32/include/sdks/_mingw_ddk.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/x86_64-w64-mingw32/include/sdks/_mingw_directx.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/x86_64-w64-mingw32/include/sec_api/stdio_s.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/x86_64-w64-mingw32/include/sec_api/string_s.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/x86_64-w64-mingw32/include/sec_api/sys/timeb_s.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/x86_64-w64-mingw32/include/sec_api/wchar_s.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/x86_64-w64-mingw32/include/signal.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/x86_64-w64-mingw32/include/stdio.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/x86_64-w64-mingw32/include/string.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/x86_64-w64-mingw32/include/swprintf.inl
 mdp:D:/Qt_5.15/Tools/mingw810_64/x86_64-w64-mingw32/include/sys/timeb.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/x86_64-w64-mingw32/include/sys/types.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/x86_64-w64-mingw32/include/vadefs.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/x86_64-w64-mingw32/include/wchar.h
 mdp:D:/Qt_5.15/Tools/mingw810_64/x86_64-w64-mingw32/include/wctype.h
 mdp:F:/Project/QT_Projact/qt/delta/vison_v4/qt/vision_widgets/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/vision_widgets_autogen/moc_predefs.h
 mdp:F:/Project/QT_Projact/qt/delta/vison_v4/qt/vision_widgets/widget.h
F:/Project/QT_Projact/qt/delta/vison_v4/qt/vision_widgets/widget.cpp
 uic:./ui_widget.h
