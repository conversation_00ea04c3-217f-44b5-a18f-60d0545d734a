#ifndef HIKVISONCAMERA_H
#define HIKVISONCAMERA_H

/**
 * @file HikvisionCamera.h
 * @brief 海康威视相机实现类
 * <AUTHOR> Module Team
 * @version 1.0.0
 * @date 2025-07-04
 */

#include "ICameraInterface.h"
#include <memory>
#include <thread>
#include <atomic>
#include <mutex>

namespace VisionModule {

/**
 * @brief 海康威视相机实现类
 * 
 * 基于海康威视MVS SDK实现的相机控制类，提供完整的相机操作功能。
 * 支持GigE和USB3.0接口的海康威视工业相机。
 */
class HikvisionCamera : public ICameraInterface {
public:
    HikvisionCamera();
    virtual ~HikvisionCamera();
    
    // 禁用拷贝构造和赋值
    HikvisionCamera(const HikvisionCamera&) = delete;
    HikvisionCamera& operator=(const HikvisionCamera&) = delete;
    
    // ICameraInterface 接口实现
    ErrorCode Connect(const std::string& deviceId) override;
    ErrorCode Disconnect() override;
    bool IsConnected() const override;
    ErrorCode GetDeviceInfo(DeviceInfo& info) const override;
    
    ErrorCode CaptureImage(cv::Mat& image) override;
    ErrorCode StartContinuousCapture(std::function<void(const cv::Mat&, uint64_t)> callback) override;
    ErrorCode StopContinuousCapture() override;
    bool IsCapturing() const override;
    
    ErrorCode SetExposure(double exposure) override;
    ErrorCode GetExposure(double& exposure) const override;
    ErrorCode SetGain(double gain) override;
    ErrorCode GetGain(double& gain) const override;
    
    ErrorCode SetImageSize(int width, int height) override;
    ErrorCode GetImageSize(int& width, int& height) const override;
    ErrorCode SetFrameRate(double frameRate) override;
    ErrorCode GetFrameRate(double& frameRate) const override;
    
    ErrorCode SetTriggerMode(const std::string& triggerMode) override;
    ErrorCode GetTriggerMode(std::string& triggerMode) const override;
    ErrorCode SoftwareTrigger() override;
    
    ErrorCode SetParameters(const CameraParameters& parameters) override;
    ErrorCode GetParameters(CameraParameters& parameters) const override;
    ErrorCode GetParameterRange(const std::string& paramName, double& minValue, double& maxValue) const override;
    
    ErrorCode ResetToDefault() override;
    ErrorCode SaveConfiguration(const std::string& filePath) override;
    ErrorCode LoadConfiguration(const std::string& filePath) override;
    
    CameraStatus GetStatus() const override;
    std::string GetLastError() const override;
    std::string GetManufacturer() const override;
    std::string GetModel() const override;
    std::string GetSDKVersion() const override;
    bool IsFeatureSupported(const std::string& feature) const override;
    
    void SetErrorCallback(std::function<void(ErrorCode, const std::string&)> callback) override;
    void SetStatusCallback(std::function<void(CameraStatus)> callback) override;
    
    /**
     * @brief 获取海康威视SDK的错误描述
     * @param errorCode SDK错误码
     * @return 错误描述字符串
     */
    static std::string GetSDKErrorDescription(int errorCode);
    
    /**
     * @brief 初始化海康威视SDK
     * @return 错误码
     */
    static ErrorCode InitializeSDK();
    
    /**
     * @brief 清理海康威视SDK
     * @return 错误码
     */
    static ErrorCode FinalizeSDK();
    
    /**
     * @brief 扫描海康威视相机设备
     * @return 设备信息列表
     */
    static std::vector<DeviceInfo> ScanDevices();

private:
    // 内部实现类（PIMPL模式）
    class Impl;
    std::unique_ptr<Impl> m_pImpl;
    
    // 状态管理
    mutable std::mutex m_statusMutex;
    std::atomic<CameraStatus> m_status;
    std::atomic<bool> m_isConnected;
    std::atomic<bool> m_isCapturing;
    
    // 错误处理
    mutable std::mutex m_errorMutex;
    std::string m_lastError;
    std::function<void(ErrorCode, const std::string&)> m_errorCallback;
    std::function<void(CameraStatus)> m_statusCallback;
    
    // 连续采集相关
    std::thread m_captureThread;
    std::atomic<bool> m_stopCapture;
    std::function<void(const cv::Mat&, uint64_t)> m_imageCallback;
    
    /**
     * @brief 设置状态
     * @param status 新状态
     */
    void SetStatus(CameraStatus status);
    
    /**
     * @brief 设置错误信息
     * @param error 错误码
     * @param message 错误消息
     */
    void SetError(ErrorCode error, const std::string& message);
    
    /**
     * @brief 连续采集线程函数
     */
    void CaptureThreadFunction();
    
    /**
     * @brief 转换海康威视SDK错误码为模块错误码
     * @param sdkError SDK错误码
     * @return 模块错误码
     */
    ErrorCode ConvertSDKError(int sdkError) const;
    
    /**
     * @brief 验证参数范围
     * @param paramName 参数名称
     * @param value 参数值
     * @return 是否有效
     */
    bool ValidateParameter(const std::string& paramName, double value) const;
    
    /**
     * @brief 获取相机句柄
     * @return 相机句柄指针
     */
    void* GetCameraHandle() const;
    
    /**
     * @brief 检查相机是否已打开
     * @return true-已打开，false-未打开
     */
    bool IsCameraOpened() const;
    
    /**
     * @brief 等待图像采集完成
     * @param timeoutMs 超时时间（毫秒）
     * @return 错误码
     */
    ErrorCode WaitForImage(int timeoutMs = 5000);
    
    /**
     * @brief 转换图像格式
     * @param srcData 源图像数据
     * @param srcSize 源图像大小
     * @param width 图像宽度
     * @param height 图像高度
     * @param pixelFormat 像素格式
     * @param dstImage 输出图像
     * @return 错误码
     */
    ErrorCode ConvertImage(const unsigned char* srcData, size_t srcSize, 
                          int width, int height, int pixelFormat, cv::Mat& dstImage);
    
    /**
     * @brief 获取像素格式字符串
     * @param pixelFormat 像素格式枚举值
     * @return 格式字符串
     */
    std::string GetPixelFormatString(int pixelFormat) const;
    
    /**
     * @brief 检查SDK是否已初始化
     * @return true-已初始化，false-未初始化
     */
    static bool IsSDKInitialized();
    
    // 静态成员
    static std::atomic<bool> s_sdkInitialized;
    static std::mutex s_sdkMutex;
    static int s_instanceCount;
};

/**
 * @brief 海康威视相机工厂类
 */
class HikvisionCameraFactory : public ICameraFactory {
public:
    std::unique_ptr<ICameraInterface> CreateCamera(const std::string& manufacturer) override;
    std::vector<std::string> GetSupportedManufacturers() const override;
    std::vector<DeviceInfo> ScanDevices(const std::string& manufacturer = "") override;
};

} // namespace VisionModule

#endif // HIKVISONCAMERA_H
