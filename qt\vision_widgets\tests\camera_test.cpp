/**
 * @file camera_test.cpp
 * @brief 相机控制模块测试
 * <AUTHOR> Module Team
 * @version 1.0.0
 * @date 2025-07-04
 */

#include <iostream>
#include <memory>
#include <chrono>
#include <thread>
#include "../src/camera/CameraController.h"
#include "../src/core/CallbackManager.h"

using namespace VisionModule;

/**
 * @brief 测试回调类
 */
class TestCallback : public IVisionCallback {
public:
    void OnImageCaptured(const cv::Mat& image, uint64_t timestamp) override {
        std::cout << "Image captured: " << image.cols << "x" << image.rows 
                  << " at " << timestamp << std::endl;
    }
    
    void OnCameraStatusChanged(CameraStatus status, const DeviceInfo& deviceInfo) override {
        std::cout << "Camera status changed: " << static_cast<int>(status) 
                  << " for device: " << deviceInfo.deviceName << std::endl;
    }
    
    void OnError(ErrorCode error, const std::string& message, const std::string& context) override {
        std::cout << "Error: " << static_cast<int>(error) << " - " << message;
        if (!context.empty()) {
            std::cout << " (Context: " << context << ")";
        }
        std::cout << std::endl;
    }
    
    void OnCameraParametersChanged(const CameraParameters& parameters) override {
        std::cout << "Camera parameters changed: Exposure=" << parameters.exposure 
                  << ", Gain=" << parameters.gain << std::endl;
    }
};

/**
 * @brief 测试相机控制器基本功能
 */
void TestCameraControllerBasics() {
    std::cout << "\n=== Testing CameraController Basics ===" << std::endl;
    
    CameraController controller;
    
    // 测试初始化
    std::cout << "Testing initialization..." << std::endl;
    ErrorCode result = controller.Initialize();
    if (result == ErrorCode::SUCCESS) {
        std::cout << "✓ Controller initialized successfully" << std::endl;
    } else {
        std::cout << "✗ Controller initialization failed: " << static_cast<int>(result) << std::endl;
        return;
    }
    
    // 测试获取支持的厂商
    std::cout << "\nTesting supported manufacturers..." << std::endl;
    auto manufacturers = controller.GetSupportedManufacturers();
    std::cout << "Supported manufacturers: ";
    for (const auto& manufacturer : manufacturers) {
        std::cout << manufacturer << " ";
    }
    std::cout << std::endl;
    
    // 测试设备扫描
    std::cout << "\nTesting device scanning..." << std::endl;
    auto devices = controller.ScanDevices();
    std::cout << "Found " << devices.size() << " devices:" << std::endl;
    for (const auto& device : devices) {
        std::cout << "  - " << device.deviceName << " (" << device.manufacturer 
                  << ", ID: " << device.deviceId << ")" << std::endl;
    }
    
    // 测试清理
    std::cout << "\nTesting finalization..." << std::endl;
    result = controller.Finalize();
    if (result == ErrorCode::SUCCESS) {
        std::cout << "✓ Controller finalized successfully" << std::endl;
    } else {
        std::cout << "✗ Controller finalization failed: " << static_cast<int>(result) << std::endl;
    }
}

/**
 * @brief 测试相机连接和参数设置
 */
void TestCameraConnection() {
    std::cout << "\n=== Testing Camera Connection ===" << std::endl;
    
    CameraController controller;
    auto callbackManager = std::make_shared<CallbackManager>();
    auto testCallback = std::make_shared<TestCallback>();
    
    // 注册回调
    callbackManager->RegisterCallback(testCallback);
    controller.SetCallbackManager(callbackManager);
    
    // 初始化
    ErrorCode result = controller.Initialize();
    if (result != ErrorCode::SUCCESS) {
        std::cout << "✗ Failed to initialize controller" << std::endl;
        return;
    }
    
    // 扫描设备
    auto devices = controller.ScanDevices();
    if (devices.empty()) {
        std::cout << "No devices found for testing" << std::endl;
        controller.Finalize();
        return;
    }
    
    // 连接第一个设备
    std::cout << "Connecting to device: " << devices[0].deviceId << std::endl;
    result = controller.ConnectCamera(devices[0].deviceId);
    if (result == ErrorCode::SUCCESS) {
        std::cout << "✓ Camera connected successfully" << std::endl;
        
        // 测试参数设置
        std::cout << "\nTesting parameter setting..." << std::endl;
        
        // 设置曝光时间
        result = controller.SetExposure(10.0);
        if (result == ErrorCode::SUCCESS) {
            std::cout << "✓ Exposure set successfully" << std::endl;
            
            double exposure;
            if (controller.GetExposure(exposure) == ErrorCode::SUCCESS) {
                std::cout << "Current exposure: " << exposure << " ms" << std::endl;
            }
        }
        
        // 设置增益
        result = controller.SetGain(5.0);
        if (result == ErrorCode::SUCCESS) {
            std::cout << "✓ Gain set successfully" << std::endl;
            
            double gain;
            if (controller.GetGain(gain) == ErrorCode::SUCCESS) {
                std::cout << "Current gain: " << gain << std::endl;
            }
        }
        
        // 测试图像采集
        std::cout << "\nTesting image capture..." << std::endl;
        cv::Mat image;
        result = controller.CaptureImage(image);
        if (result == ErrorCode::SUCCESS) {
            std::cout << "✓ Image captured successfully: " << image.cols << "x" << image.rows << std::endl;
        } else {
            std::cout << "✗ Image capture failed: " << static_cast<int>(result) << std::endl;
        }
        
        // 断开连接
        std::cout << "\nDisconnecting camera..." << std::endl;
        result = controller.DisconnectCamera();
        if (result == ErrorCode::SUCCESS) {
            std::cout << "✓ Camera disconnected successfully" << std::endl;
        }
        
    } else {
        std::cout << "✗ Camera connection failed: " << static_cast<int>(result) << std::endl;
    }
    
    controller.Finalize();
}

/**
 * @brief 测试连续采集
 */
void TestContinuousCapture() {
    std::cout << "\n=== Testing Continuous Capture ===" << std::endl;
    
    CameraController controller;
    
    // 初始化
    ErrorCode result = controller.Initialize();
    if (result != ErrorCode::SUCCESS) {
        std::cout << "✗ Failed to initialize controller" << std::endl;
        return;
    }
    
    // 扫描设备
    auto devices = controller.ScanDevices();
    if (devices.empty()) {
        std::cout << "No devices found for testing" << std::endl;
        controller.Finalize();
        return;
    }
    
    // 连接设备
    result = controller.ConnectCamera(devices[0].deviceId);
    if (result != ErrorCode::SUCCESS) {
        std::cout << "✗ Failed to connect camera" << std::endl;
        controller.Finalize();
        return;
    }
    
    // 开始连续采集
    std::cout << "Starting continuous capture..." << std::endl;
    int captureCount = 0;
    result = controller.StartContinuousCapture([&captureCount](const cv::Mat& image, uint64_t timestamp) {
        captureCount++;
        std::cout << "Captured frame " << captureCount << ": " << image.cols << "x" << image.rows 
                  << " at " << timestamp << std::endl;
    });
    
    if (result == ErrorCode::SUCCESS) {
        std::cout << "✓ Continuous capture started" << std::endl;
        
        // 运行5秒
        std::this_thread::sleep_for(std::chrono::seconds(5));
        
        // 停止采集
        std::cout << "Stopping continuous capture..." << std::endl;
        result = controller.StopContinuousCapture();
        if (result == ErrorCode::SUCCESS) {
            std::cout << "✓ Continuous capture stopped" << std::endl;
            std::cout << "Total frames captured: " << captureCount << std::endl;
        }
    } else {
        std::cout << "✗ Failed to start continuous capture: " << static_cast<int>(result) << std::endl;
    }
    
    controller.DisconnectCamera();
    controller.Finalize();
}

/**
 * @brief 主测试函数
 */
int main() {
    std::cout << "Vision Module Camera Test" << std::endl;
    std::cout << "=========================" << std::endl;
    
    try {
        // 运行基本功能测试
        TestCameraControllerBasics();
        
        // 运行连接测试
        TestCameraConnection();
        
        // 运行连续采集测试
        TestContinuousCapture();
        
        std::cout << "\n=== All Tests Completed ===" << std::endl;
        
    } catch (const std::exception& e) {
        std::cout << "Test failed with exception: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
