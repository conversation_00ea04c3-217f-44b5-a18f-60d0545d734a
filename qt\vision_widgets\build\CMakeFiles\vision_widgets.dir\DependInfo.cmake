
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "" "vision_widgets_autogen/timestamp" "custom" "vision_widgets_autogen/deps"
  "F:/Project/QT_Projact/qt/delta/vison_v4/qt/vision_widgets/main.cpp" "CMakeFiles/vision_widgets.dir/main.cpp.obj" "gcc" "CMakeFiles/vision_widgets.dir/main.cpp.obj.d"
  "F:/Project/QT_Projact/qt/delta/vison_v4/qt/vision_widgets/build/vision_widgets_autogen/mocs_compilation.cpp" "CMakeFiles/vision_widgets.dir/vision_widgets_autogen/mocs_compilation.cpp.obj" "gcc" "CMakeFiles/vision_widgets.dir/vision_widgets_autogen/mocs_compilation.cpp.obj.d"
  "F:/Project/QT_Projact/qt/delta/vison_v4/qt/vision_widgets/widget.cpp" "CMakeFiles/vision_widgets.dir/widget.cpp.obj" "gcc" "CMakeFiles/vision_widgets.dir/widget.cpp.obj.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
