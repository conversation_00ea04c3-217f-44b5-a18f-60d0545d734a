{"version": "0.2.0", "configurations": [{"name": "Debug Qt Project", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/qt/vision_widgets/build/vision_widgets.exe", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}/qt/vision_widgets", "environment": [{"name": "PATH", "value": "D:/Qt_5.15/5.15.2/mingw81_64/bin;${env:PATH}"}], "externalConsole": false, "MIMode": "gdb", "miDebuggerPath": "D:/Qt_5.15/Tools/mingw810_64/bin/gdb.exe", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}], "preLaunchTask": "CMake Build", "visualizerFile": "c:\\Users\\<USER>\\AppData\\Roaming\\Trae\\User\\workspaceStorage\\9327cecd11c0c419176d66acd386495e\\tonka3000.qtvsctools\\qt.natvis.xml"}]}