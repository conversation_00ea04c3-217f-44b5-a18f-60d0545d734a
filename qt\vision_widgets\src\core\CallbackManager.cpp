#include "IVisionCallback.h"
#include <algorithm>
#include <chrono>

namespace VisionModule
{

    void CallbackManager::RegisterCallback(std::shared_ptr<IVisionCallback> callback)
    {
        if (callback)
        {
            // 检查是否已存在
            for (auto it = m_callbacks.begin(); it != m_callbacks.end(); ++it)
            {
                if (auto existing = it->lock())
                {
                    if (existing == callback)
                    {
                        return; // 已存在，不重复添加
                    }
                }
            }
            m_callbacks.push_back(std::weak_ptr<IVisionCallback>(callback));
        }
    }

    void CallbackManager::RegisterSimpleCallback(std::shared_ptr<ISimpleVisionCallback> callback)
    {
        if (callback)
        {
            // 检查是否已存在
            for (auto it = m_simpleCallbacks.begin(); it != m_simpleCallbacks.end(); ++it)
            {
                if (auto existing = it->lock())
                {
                    if (existing == callback)
                    {
                        return; // 已存在，不重复添加
                    }
                }
            }
            m_simpleCallbacks.push_back(std::weak_ptr<ISimpleVisionCallback>(callback));
        }
    }

    void CallbackManager::UnregisterCallback(std::shared_ptr<IVisionCallback> callback)
    {
        if (callback)
        {
            m_callbacks.erase(
                std::remove_if(m_callbacks.begin(), m_callbacks.end(),
                               [callback](const std::weak_ptr<IVisionCallback> &wp)
                               {
                                   auto sp = wp.lock();
                                   return !sp || sp == callback;
                               }),
                m_callbacks.end());
        }
    }

    void CallbackManager::UnregisterSimpleCallback(std::shared_ptr<ISimpleVisionCallback> callback)
    {
        if (callback)
        {
            m_simpleCallbacks.erase(
                std::remove_if(m_simpleCallbacks.begin(), m_simpleCallbacks.end(),
                               [callback](const std::weak_ptr<ISimpleVisionCallback> &wp)
                               {
                                   auto sp = wp.lock();
                                   return !sp || sp == callback;
                               }),
                m_simpleCallbacks.end());
        }
    }

    void CallbackManager::ClearAllCallbacks()
    {
        m_callbacks.clear();
        m_simpleCallbacks.clear();
    }

    void CallbackManager::TriggerImageCaptured(const cv::Mat &image, uint64_t timestamp)
    {
        if (timestamp == 0)
        {
            timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
                            std::chrono::system_clock::now().time_since_epoch())
                            .count();
        }

        // 触发完整回调接口
        for (auto &callback : m_callbacks)
        {
            if (auto cb = callback.lock())
            {
                try
                {
                    cb->OnImageCaptured(image, timestamp);
                }
                catch (...)
                {
                    // 忽略回调中的异常，避免影响其他回调
                }
            }
        }

        // 触发简化回调接口
        for (auto &callback : m_simpleCallbacks)
        {
            if (auto cb = callback.lock())
            {
                try
                {
                    cb->OnImageCaptured(image);
                }
                catch (...)
                {
                    // 忽略回调中的异常
                }
            }
        }

        // 清理失效的弱引用
        m_callbacks.erase(
            std::remove_if(m_callbacks.begin(), m_callbacks.end(),
                           [](const std::weak_ptr<IVisionCallback> &wp)
                           { return wp.expired(); }),
            m_callbacks.end());

        m_simpleCallbacks.erase(
            std::remove_if(m_simpleCallbacks.begin(), m_simpleCallbacks.end(),
                           [](const std::weak_ptr<ISimpleVisionCallback> &wp)
                           { return wp.expired(); }),
            m_simpleCallbacks.end());
    }

    void CallbackManager::TriggerMatchFound(const std::vector<MatchResult> &results, double processingTime)
    {
        // 触发完整回调接口
        for (auto &callback : m_callbacks)
        {
            if (auto cb = callback.lock())
            {
                try
                {
                    cb->OnMatchFound(results, processingTime);
                }
                catch (...)
                {
                    // 忽略回调中的异常
                }
            }
        }

        // 为简化回调接口转换数据格式
        std::vector<Coordinate2D> simpleResults;
        for (const auto &result : results)
        {
            simpleResults.push_back(result.coordinate);
        }

        // 触发简化回调接口
        for (auto &callback : m_simpleCallbacks)
        {
            if (auto cb = callback.lock())
            {
                try
                {
                    cb->OnMatchFound(simpleResults);
                }
                catch (...)
                {
                    // 忽略回调中的异常
                }
            }
        }
    }

    void CallbackManager::TriggerError(ErrorCode error, const std::string &message, const std::string &context)
    {
        // 触发完整回调接口
        for (auto &callback : m_callbacks)
        {
            if (auto cb = callback.lock())
            {
                try
                {
                    cb->OnError(error, message, context);
                }
                catch (...)
                {
                    // 忽略回调中的异常
                }
            }
        }

        // 触发简化回调接口
        for (auto &callback : m_simpleCallbacks)
        {
            if (auto cb = callback.lock())
            {
                try
                {
                    cb->OnError(error, message);
                }
                catch (...)
                {
                    // 忽略回调中的异常
                }
            }
        }
    }

    void CallbackManager::TriggerStatusChanged(SystemStatus status, const std::string &message)
    {
        // 触发完整回调接口
        for (auto &callback : m_callbacks)
        {
            if (auto cb = callback.lock())
            {
                try
                {
                    cb->OnSystemStatusChanged(status, message);
                }
                catch (...)
                {
                    // 忽略回调中的异常
                }
            }
        }

        // 为简化回调接口生成状态字符串
        std::string statusString = std::string(GetSystemStatusString(status));
        if (!message.empty())
        {
            statusString += ": " + message;
        }

        // 触发简化回调接口
        for (auto &callback : m_simpleCallbacks)
        {
            if (auto cb = callback.lock())
            {
                try
                {
                    cb->OnStatusChanged(statusString);
                }
                catch (...)
                {
                    // 忽略回调中的异常
                }
            }
        }
    }

    void CallbackManager::TriggerCameraStatusChanged(CameraStatus status, const DeviceInfo &deviceInfo)
    {
        // 触发完整回调接口
        for (auto &callback : m_callbacks)
        {
            if (auto cb = callback.lock())
            {
                try
                {
                    cb->OnCameraStatusChanged(status, deviceInfo);
                }
                catch (...)
                {
                    // 忽略回调中的异常
                }
            }
        }
    }

    void CallbackManager::TriggerCameraParametersChanged(const CameraParameters &parameters)
    {
        // 触发完整回调接口
        for (auto &callback : m_callbacks)
        {
            if (auto cb = callback.lock())
            {
                try
                {
                    cb->OnCameraParametersChanged(parameters);
                }
                catch (...)
                {
                    // 忽略回调中的异常
                }
            }
        }
    }

    void CallbackManager::TriggerFileOperation(const std::string &operation, const std::string &filePath, bool success)
    {
        // 触发完整回调接口
        for (auto &callback : m_callbacks)
        {
            if (auto cb = callback.lock())
            {
                try
                {
                    cb->OnFileOperation(operation, filePath, success);
                }
                catch (...)
                {
                    // 忽略回调中的异常
                }
            }
        }
    }

} // namespace VisionModule
