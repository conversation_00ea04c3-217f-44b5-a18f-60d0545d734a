{"inputs": [{"path": "CMakeLists.txt"}, {"isGenerated": true, "path": "build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/.qtc/package-manager/auto-setup.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeSystem.cmake.in"}, {"isGenerated": true, "path": "build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/CMakeFiles/3.30.5/CMakeSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeMinGWFindMake.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows-Initialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows-Determine-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeCompilerIdDetection.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/ADSP-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/ARMCC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/ARMClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/AppleClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/Borland-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/Clang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/Cray-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/CrayClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/Embarcadero-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/Fujitsu-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/GHS-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/HP-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/IAR-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/IBMClang-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/Intel-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/LCC-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/MSVC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/NVHPC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/NVIDIA-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/OrangeC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/PGI-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/PathScale-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/SCO-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/TI-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/TIClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/Tasking-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/Watcom-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/XL-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeFindBinUtils.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/GNU-FindBinUtils.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeCXXCompiler.cmake.in"}, {"isGenerated": true, "path": "build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/CMakeFiles/3.30.5/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Platform/WindowsPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeCXXInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/GNU-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows-GNU-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows-GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineRCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeRCCompiler.cmake.in"}, {"isGenerated": true, "path": "build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/CMakeFiles/3.30.5/CMakeRCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeRCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows-windres.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeTestRCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeTestCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeTestCompilerCommon.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Internal/CMakeDetermineLinkerId.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeParseImplicitIncludeInfo.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeParseImplicitLinkInfo.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeParseLibraryArchitecture.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeTestCompilerCommon.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeCXXCompilerABI.cpp"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerSupport.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Internal/FeatureTesting.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeCXXCompiler.cmake.in"}, {"isGenerated": true, "path": "build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug/CMakeFiles/3.30.5/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows-GNU-CXX-ABI.cmake"}, {"isExternal": true, "path": "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5/Qt5ConfigVersion.cmake"}, {"isExternal": true, "path": "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5/Qt5Config.cmake"}, {"isExternal": true, "path": "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5/Qt5ConfigVersion.cmake"}, {"isExternal": true, "path": "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5/Qt5Config.cmake"}, {"isExternal": true, "path": "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5/Qt5ModuleLocation.cmake"}, {"isExternal": true, "path": "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Widgets/Qt5WidgetsConfigVersion.cmake"}, {"isExternal": true, "path": "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Widgets/Qt5WidgetsConfig.cmake"}, {"isExternal": true, "path": "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Gui/Qt5GuiConfigVersion.cmake"}, {"isExternal": true, "path": "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Gui/Qt5GuiConfig.cmake"}, {"isExternal": true, "path": "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Core/Qt5CoreConfigVersion.cmake"}, {"isExternal": true, "path": "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Core/Qt5CoreConfig.cmake"}, {"isExternal": true, "path": "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Core/Qt5CoreConfigExtras.cmake"}, {"isExternal": true, "path": "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Core/Qt5CoreConfigExtrasMkspecDir.cmake"}, {"isExternal": true, "path": "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Core/Qt5CoreMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeParseArguments.cmake"}, {"isExternal": true, "path": "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QGifPlugin.cmake"}, {"isExternal": true, "path": "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QICNSPlugin.cmake"}, {"isExternal": true, "path": "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QICOPlugin.cmake"}, {"isExternal": true, "path": "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QJpegPlugin.cmake"}, {"isExternal": true, "path": "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QMinimalIntegrationPlugin.cmake"}, {"isExternal": true, "path": "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QOffscreenIntegrationPlugin.cmake"}, {"isExternal": true, "path": "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QSvgIconPlugin.cmake"}, {"isExternal": true, "path": "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QSvgPlugin.cmake"}, {"isExternal": true, "path": "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QTgaPlugin.cmake"}, {"isExternal": true, "path": "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QTiffPlugin.cmake"}, {"isExternal": true, "path": "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QTuioTouchPlugin.cmake"}, {"isExternal": true, "path": "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QWbmpPlugin.cmake"}, {"isExternal": true, "path": "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QWebpPlugin.cmake"}, {"isExternal": true, "path": "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QWindowsDirect2DIntegrationPlugin.cmake"}, {"isExternal": true, "path": "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QWindowsIntegrationPlugin.cmake"}, {"isExternal": true, "path": "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QXdgDesktopPortalThemePlugin.cmake"}, {"isExternal": true, "path": "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Gui/Qt5GuiConfigExtras.cmake"}, {"isExternal": true, "path": "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Widgets/Qt5Widgets_QWindowsVistaStylePlugin.cmake"}, {"isExternal": true, "path": "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Widgets/Qt5WidgetsConfigExtras.cmake"}, {"isExternal": true, "path": "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5Widgets/Qt5WidgetsMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeParseArguments.cmake"}, {"isExternal": true, "path": "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5LinguistTools/Qt5LinguistToolsConfigVersion.cmake"}, {"isExternal": true, "path": "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5LinguistTools/Qt5LinguistToolsConfig.cmake"}, {"isExternal": true, "path": "D:/Qt_5.15/5.15.2/mingw81_64/lib/cmake/Qt5LinguistTools/Qt5LinguistToolsMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/CMakeParseArguments.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Qt_5.15/Tools/CMake_64/share/cmake-3.30/Modules/GNUInstallDirs.cmake"}], "kind": "cmakeFiles", "paths": {"build": "F:/Project/QT_Projact/qt/delta/vison_v4/qt/vision_widgets/build/Desktop_Qt_5_15_2_MinGW_64_bit-Debug", "source": "F:/Project/QT_Projact/qt/delta/vison_v4/qt/vision_widgets"}, "version": {"major": 1, "minor": 1}}