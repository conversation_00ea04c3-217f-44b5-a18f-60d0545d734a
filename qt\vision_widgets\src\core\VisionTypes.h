#ifndef VISIONTYPES_H
#define VISIONTYPES_H

/**
 * @file VisionTypes.h
 * @brief 视觉模块核心数据类型定义
 * <AUTHOR> Module Team
 * @version 1.0.0
 * @date 2025-07-04
 */

#include <string>
#include <vector>
#include <memory>
#include <functional>
#include "ErrorCodes.h"

// 前向声明
namespace cv {
    class Mat;
}

namespace VisionModule {

/**
 * @brief 2D点坐标结构
 */
struct Point2D {
    double x = 0.0;  ///< X坐标
    double y = 0.0;  ///< Y坐标
    
    Point2D() = default;
    Point2D(double x, double y) : x(x), y(y) {}
    
    // 运算符重载
    Point2D operator+(const Point2D& other) const {
        return Point2D(x + other.x, y + other.y);
    }
    
    Point2D operator-(const Point2D& other) const {
        return Point2D(x - other.x, y - other.y);
    }
    
    Point2D operator*(double scale) const {
        return Point2D(x * scale, y * scale);
    }
    
    bool operator==(const Point2D& other) const {
        return (std::abs(x - other.x) < 1e-6) && (std::abs(y - other.y) < 1e-6);
    }
    
    /**
     * @brief 计算到另一点的距离
     */
    double distanceTo(const Point2D& other) const;
};

/**
 * @brief 物理坐标结构（包含角度和置信度）
 */
struct Coordinate2D {
    double x = 0.0;          ///< X坐标 (mm)
    double y = 0.0;          ///< Y坐标 (mm)
    double angle = 0.0;      ///< 角度 (度)
    double confidence = 0.0; ///< 置信度 (0.0-1.0)
    
    Coordinate2D() = default;
    Coordinate2D(double x, double y, double angle = 0.0, double confidence = 1.0)
        : x(x), y(y), angle(angle), confidence(confidence) {}
    
    /**
     * @brief 检查坐标是否有效
     */
    bool isValid() const {
        return confidence > 0.0 && confidence <= 1.0;
    }
    
    /**
     * @brief 转换为字符串表示
     */
    std::string toString() const;
};

/**
 * @brief 矩形区域结构
 */
struct Rectangle {
    int x = 0;       ///< 左上角X坐标
    int y = 0;       ///< 左上角Y坐标
    int width = 0;   ///< 宽度
    int height = 0;  ///< 高度
    
    Rectangle() = default;
    Rectangle(int x, int y, int w, int h) : x(x), y(y), width(w), height(h) {}
    
    /**
     * @brief 检查矩形是否有效
     */
    bool isValid() const {
        return width > 0 && height > 0;
    }
    
    /**
     * @brief 获取矩形面积
     */
    int area() const {
        return width * height;
    }
    
    /**
     * @brief 获取矩形中心点
     */
    Point2D center() const {
        return Point2D(x + width / 2.0, y + height / 2.0);
    }
    
    /**
     * @brief 检查点是否在矩形内
     */
    bool contains(const Point2D& point) const;
    
    /**
     * @brief 检查与另一矩形是否相交
     */
    bool intersects(const Rectangle& other) const;
};

/**
 * @brief 相机参数结构
 */
struct CameraParameters {
    double exposure = 10.0;      ///< 曝光时间 (ms)
    double gain = 1.0;           ///< 增益
    int width = 1920;            ///< 图像宽度
    int height = 1080;           ///< 图像高度
    std::string format = "RGB8"; ///< 图像格式
    bool autoExposure = false;   ///< 自动曝光
    bool autoGain = false;       ///< 自动增益
    double frameRate = 30.0;     ///< 帧率
    
    /**
     * @brief 检查参数是否有效
     */
    bool isValid() const;
    
    /**
     * @brief 转换为字符串表示
     */
    std::string toString() const;
};

/**
 * @brief 模板匹配参数结构
 */
struct MatchingParameters {
    double threshold = 0.8;      ///< 匹配阈值 (0.0-1.0)
    int maxMatches = 5;          ///< 最大匹配数量
    double angleRange = 360.0;   ///< 角度搜索范围 (度)
    double scaleMin = 0.8;       ///< 最小缩放比例
    double scaleMax = 1.2;       ///< 最大缩放比例
    bool enableSubPixel = true;  ///< 启用亚像素精度
    int pyramidLevels = 3;       ///< 金字塔层数
    
    /**
     * @brief 检查参数是否有效
     */
    bool isValid() const;
    
    /**
     * @brief 转换为字符串表示
     */
    std::string toString() const;
};

/**
 * @brief 标定参数结构
 */
struct CalibrationParameters {
    std::vector<Point2D> pixelPoints;     ///< 像素坐标点
    std::vector<Coordinate2D> worldPoints; ///< 世界坐标点
    double accuracy = 0.0;                 ///< 标定精度 (mm)
    std::string method = "ThreePoint";     ///< 标定方法
    bool isValid = false;                  ///< 标定是否有效
    
    /**
     * @brief 添加标定点对
     */
    void addPointPair(const Point2D& pixel, const Coordinate2D& world);
    
    /**
     * @brief 清除所有标定点
     */
    void clear();
    
    /**
     * @brief 获取标定点数量
     */
    size_t getPointCount() const {
        return std::min(pixelPoints.size(), worldPoints.size());
    }
};

/**
 * @brief 设备信息结构
 */
struct DeviceInfo {
    std::string deviceId;        ///< 设备ID
    std::string deviceName;      ///< 设备名称
    std::string manufacturer;    ///< 制造商
    std::string model;           ///< 型号
    std::string serialNumber;    ///< 序列号
    std::string version;         ///< 版本
    bool isConnected = false;    ///< 是否已连接
    
    /**
     * @brief 转换为字符串表示
     */
    std::string toString() const;
};

/**
 * @brief 匹配结果结构
 */
struct MatchResult {
    Coordinate2D coordinate;     ///< 匹配坐标
    double score = 0.0;          ///< 匹配分数
    Rectangle boundingBox;       ///< 边界框
    std::string templateName;    ///< 模板名称
    int matchIndex = -1;         ///< 匹配索引
    
    /**
     * @brief 检查结果是否有效
     */
    bool isValid() const {
        return score > 0.0 && coordinate.isValid();
    }
};

/**
 * @brief 系统状态枚举
 */
enum class SystemStatus {
    Uninitialized,    ///< 未初始化
    Initializing,     ///< 初始化中
    Ready,            ///< 就绪
    Running,          ///< 运行中
    Error,            ///< 错误状态
    Stopped           ///< 已停止
};

/**
 * @brief 相机状态枚举
 */
enum class CameraStatus {
    Disconnected,     ///< 未连接
    Connecting,       ///< 连接中
    Connected,        ///< 已连接
    Capturing,        ///< 采集中
    Error             ///< 错误状态
};

/**
 * @brief 标定状态枚举
 */
enum class CalibrationStatus {
    NotCalibrated,    ///< 未标定
    Calibrating,      ///< 标定中
    Calibrated,       ///< 已标定
    CalibrationError  ///< 标定错误
};

/**
 * @brief 获取系统状态字符串
 */
const char* GetSystemStatusString(SystemStatus status);

/**
 * @brief 获取相机状态字符串
 */
const char* GetCameraStatusString(CameraStatus status);

/**
 * @brief 获取标定状态字符串
 */
const char* GetCalibrationStatusString(CalibrationStatus status);

} // namespace VisionModule

#endif // VISIONTYPES_H
