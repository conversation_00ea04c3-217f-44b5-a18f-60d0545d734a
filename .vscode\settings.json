{"files.associations": {"*.ui": "xml", "*.qrc": "xml", "*.ts": "xml", "*.cpp": "cpp", "*.h": "cpp", "*.hpp": "cpp", "*.c": "c", "*.pro": "makefile", "*.pri": "makefile"}, "C_Cpp.default.configurationProvider": "ms-vscode.cmake-tools", "editor.formatOnSave": true, "editor.formatOnType": true, "editor.rulers": [100], "editor.tabSize": 4, "editor.insertSpaces": true, "editor.detectIndentation": true, "cmake.configureOnOpen": true, "cmake.buildDirectory": "${workspaceFolder}/qt/vision_widgets/build", "cmake.sourceDirectory": "${workspaceFolder}/qt/vision_widgets"}