# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.30

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = D:\Qt_5.15\Tools\CMake_64\bin\cmake.exe

# The command to remove a file.
RM = D:\Qt_5.15\Tools\CMake_64\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = F:\Project\QT_Projact\qt\delta\vison_v4\qt\vision_widgets

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = F:\Project\QT_Projact\qt\delta\vison_v4\qt\vision_widgets\build\Desktop_Qt_5_15_2_MinGW_64_bit-Debug

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	D:\Qt_5.15\Tools\CMake_64\bin\cmake-gui.exe -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	D:\Qt_5.15\Tools\CMake_64\bin\cmake.exe --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	D:\Qt_5.15\Tools\CMake_64\bin\cmake.exe -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	D:\Qt_5.15\Tools\CMake_64\bin\cmake.exe -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	D:\Qt_5.15\Tools\CMake_64\bin\cmake.exe -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	D:\Qt_5.15\Tools\CMake_64\bin\cmake.exe -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	D:\Qt_5.15\Tools\CMake_64\bin\cmake.exe -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	D:\Qt_5.15\Tools\CMake_64\bin\cmake.exe -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start F:\Project\QT_Projact\qt\delta\vison_v4\qt\vision_widgets\build\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\CMakeFiles F:\Project\QT_Projact\qt\delta\vison_v4\qt\vision_widgets\build\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\\CMakeFiles\progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start F:\Project\QT_Projact\qt\delta\vison_v4\qt\vision_widgets\build\Desktop_Qt_5_15_2_MinGW_64_bit-Debug\CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named vision_widgets

# Build rule for target.
vision_widgets: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 vision_widgets
.PHONY : vision_widgets

# fast build rule for target.
vision_widgets/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\vision_widgets.dir\build.make CMakeFiles/vision_widgets.dir/build
.PHONY : vision_widgets/fast

#=============================================================================
# Target rules for targets named vision_widgets_autogen_timestamp_deps

# Build rule for target.
vision_widgets_autogen_timestamp_deps: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 vision_widgets_autogen_timestamp_deps
.PHONY : vision_widgets_autogen_timestamp_deps

# fast build rule for target.
vision_widgets_autogen_timestamp_deps/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\vision_widgets_autogen_timestamp_deps.dir\build.make CMakeFiles/vision_widgets_autogen_timestamp_deps.dir/build
.PHONY : vision_widgets_autogen_timestamp_deps/fast

#=============================================================================
# Target rules for targets named vision_widgets_autogen

# Build rule for target.
vision_widgets_autogen: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 vision_widgets_autogen
.PHONY : vision_widgets_autogen

# fast build rule for target.
vision_widgets_autogen/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\vision_widgets_autogen.dir\build.make CMakeFiles/vision_widgets_autogen.dir/build
.PHONY : vision_widgets_autogen/fast

main.obj: main.cpp.obj
.PHONY : main.obj

# target to build an object file
main.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\vision_widgets.dir\build.make CMakeFiles/vision_widgets.dir/main.cpp.obj
.PHONY : main.cpp.obj

main.i: main.cpp.i
.PHONY : main.i

# target to preprocess a source file
main.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\vision_widgets.dir\build.make CMakeFiles/vision_widgets.dir/main.cpp.i
.PHONY : main.cpp.i

main.s: main.cpp.s
.PHONY : main.s

# target to generate assembly for a file
main.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\vision_widgets.dir\build.make CMakeFiles/vision_widgets.dir/main.cpp.s
.PHONY : main.cpp.s

vision_widgets_autogen/mocs_compilation.obj: vision_widgets_autogen/mocs_compilation.cpp.obj
.PHONY : vision_widgets_autogen/mocs_compilation.obj

# target to build an object file
vision_widgets_autogen/mocs_compilation.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\vision_widgets.dir\build.make CMakeFiles/vision_widgets.dir/vision_widgets_autogen/mocs_compilation.cpp.obj
.PHONY : vision_widgets_autogen/mocs_compilation.cpp.obj

vision_widgets_autogen/mocs_compilation.i: vision_widgets_autogen/mocs_compilation.cpp.i
.PHONY : vision_widgets_autogen/mocs_compilation.i

# target to preprocess a source file
vision_widgets_autogen/mocs_compilation.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\vision_widgets.dir\build.make CMakeFiles/vision_widgets.dir/vision_widgets_autogen/mocs_compilation.cpp.i
.PHONY : vision_widgets_autogen/mocs_compilation.cpp.i

vision_widgets_autogen/mocs_compilation.s: vision_widgets_autogen/mocs_compilation.cpp.s
.PHONY : vision_widgets_autogen/mocs_compilation.s

# target to generate assembly for a file
vision_widgets_autogen/mocs_compilation.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\vision_widgets.dir\build.make CMakeFiles/vision_widgets.dir/vision_widgets_autogen/mocs_compilation.cpp.s
.PHONY : vision_widgets_autogen/mocs_compilation.cpp.s

widget.obj: widget.cpp.obj
.PHONY : widget.obj

# target to build an object file
widget.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\vision_widgets.dir\build.make CMakeFiles/vision_widgets.dir/widget.cpp.obj
.PHONY : widget.cpp.obj

widget.i: widget.cpp.i
.PHONY : widget.i

# target to preprocess a source file
widget.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\vision_widgets.dir\build.make CMakeFiles/vision_widgets.dir/widget.cpp.i
.PHONY : widget.cpp.i

widget.s: widget.cpp.s
.PHONY : widget.s

# target to generate assembly for a file
widget.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\vision_widgets.dir\build.make CMakeFiles/vision_widgets.dir/widget.cpp.s
.PHONY : widget.cpp.s

# Help Target
help:
	@echo The following are some of the valid targets for this Makefile:
	@echo ... all (the default if no target is provided)
	@echo ... clean
	@echo ... depend
	@echo ... edit_cache
	@echo ... install
	@echo ... install/local
	@echo ... install/strip
	@echo ... list_install_components
	@echo ... rebuild_cache
	@echo ... vision_widgets_autogen
	@echo ... vision_widgets_autogen_timestamp_deps
	@echo ... vision_widgets
	@echo ... main.obj
	@echo ... main.i
	@echo ... main.s
	@echo ... vision_widgets_autogen/mocs_compilation.obj
	@echo ... vision_widgets_autogen/mocs_compilation.i
	@echo ... vision_widgets_autogen/mocs_compilation.s
	@echo ... widget.obj
	@echo ... widget.i
	@echo ... widget.s
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

